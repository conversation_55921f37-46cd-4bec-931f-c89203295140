<?php
/**
 * إعداد مبسط لنظام إدارة التبرعات
 * جمعية قوافل الخير
 */

$step = $_GET['step'] ?? 1;
$errors = [];
$success = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step == 1) {
        // إعداد قاعدة البيانات
        $dbHost = $_POST['db_host'] ?? 'localhost';
        $dbName = $_POST['db_name'] ?? 'qawafil_donations';
        $dbUser = $_POST['db_user'] ?? 'root';
        $dbPass = $_POST['db_pass'] ?? '';
        $systemUrl = $_POST['system_url'] ?? 'http://localhost/mosra-kher';
        
        try {
            // اختبار الاتصال
            $pdo = new PDO("mysql:host=$dbHost;charset=utf8mb4", $dbUser, $dbPass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // إنشاء قاعدة البيانات
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $pdo->exec("USE `$dbName`");
            
            // تشغيل ملف إعداد قاعدة البيانات
            $sql = file_get_contents('database/setup.sql');
            $sql = str_replace('qawafil_donations', $dbName, $sql);
            
            // تقسيم الاستعلامات وتنفيذها
            $statements = array_filter(array_map('trim', explode(';', $sql)));
            foreach ($statements as $statement) {
                if (!empty($statement)) {
                    $pdo->exec($statement);
                }
            }
            
            // إنشاء ملف الإعدادات
            $configContent = createConfigFile($dbHost, $dbName, $dbUser, $dbPass, $systemUrl);
            file_put_contents('includes/config.php', $configContent);
            
            $success[] = 'تم إعداد النظام بنجاح!';
            $step = 2;
            
        } catch (Exception $e) {
            $errors[] = 'خطأ في إعداد قاعدة البيانات: ' . $e->getMessage();
        }
    }
}

function createConfigFile($dbHost, $dbName, $dbUser, $dbPass, $systemUrl) {
    return '<?php
/**
 * ملف الإعدادات الأساسي لنظام إدارة التبرعات
 * جمعية قوافل الخير
 */

// تعريف ثابت الوصول للنظام
define(\'SYSTEM_ACCESS\', true);

// إعدادات قاعدة البيانات
define(\'DB_HOST\', \'' . addslashes($dbHost) . '\');
define(\'DB_NAME\', \'' . addslashes($dbName) . '\');
define(\'DB_USER\', \'' . addslashes($dbUser) . '\');
define(\'DB_PASS\', \'' . addslashes($dbPass) . '\');
define(\'DB_CHARSET\', \'utf8mb4\');

// إعدادات النظام
define(\'SYSTEM_NAME\', \'نظام إدارة التبرعات - جمعية قوافل الخير\');
define(\'SYSTEM_VERSION\', \'1.0.0\');
define(\'SYSTEM_URL\', \'' . addslashes($systemUrl) . '\');
define(\'ADMIN_URL\', SYSTEM_URL . \'/admin\');
define(\'PUBLIC_URL\', SYSTEM_URL . \'/public\');

// مسارات الملفات
define(\'ROOT_PATH\', dirname(__DIR__));
define(\'INCLUDES_PATH\', ROOT_PATH . \'/includes\');
define(\'UPLOADS_PATH\', ROOT_PATH . \'/uploads\');
define(\'RECEIPTS_PATH\', UPLOADS_PATH . \'/receipts\');
define(\'BACKUPS_PATH\', UPLOADS_PATH . \'/backups\');

// إعدادات الجلسة
define(\'SESSION_NAME\', \'qawafil_session\');
define(\'SESSION_LIFETIME\', 3600);

// إعدادات الأمان
define(\'HASH_ALGO\', \'sha256\');
define(\'ENCRYPTION_KEY\', \'qawafil_donations_2024_secret_key\');

// إعدادات التحميل
define(\'MAX_FILE_SIZE\', 5 * 1024 * 1024);
define(\'ALLOWED_EXTENSIONS\', [\'pdf\', \'jpg\', \'jpeg\', \'png\']);

// إعدادات PDF
define(\'PDF_FONT\', \'DejaVuSans\');
define(\'PDF_FONT_SIZE\', 12);

// إعدادات التاريخ والوقت
date_default_timezone_set(\'Asia/Riyadh\');
define(\'DATE_FORMAT\', \'Y-m-d\');
define(\'DATETIME_FORMAT\', \'Y-m-d H:i:s\');
define(\'DISPLAY_DATE_FORMAT\', \'d/m/Y\');
define(\'DISPLAY_DATETIME_FORMAT\', \'d/m/Y H:i\');

// إعدادات اللغة
define(\'SYSTEM_LANG\', \'ar\');
define(\'SYSTEM_DIRECTION\', \'rtl\');

// إعدادات التصحيح
define(\'DEBUG_MODE\', true);
define(\'LOG_ERRORS\', true);
define(\'ERROR_LOG_FILE\', ROOT_PATH . \'/logs/error.log\');

// أنواع التبرعات
define(\'DONATION_TYPES\', [
    \'monthly_sponsorship\' => \'كفالة شهرية\',
    \'restricted\' => \'تبرع مقيد\',
    \'unrestricted\' => \'تبرع غير مقيد\'
]);

// طرق الدفع
define(\'PAYMENT_METHODS\', [
    \'cash\' => \'نقدي\',
    \'bank_transfer\' => \'تحويل بنكي\',
    \'card\' => \'بطاقة ائتمانية\',
    \'online\' => \'دفع إلكتروني\'
]);

// مستويات الصلاحيات
define(\'USER_ROLES\', [
    \'admin\' => \'مدير النظام\',
    \'employee\' => \'موظف\',
    \'user\' => \'مستخدم\'
]);

// الصلاحيات المتاحة
define(\'PERMISSIONS\', [
    \'users\' => \'إدارة المستخدمين\',
    \'donations\' => \'إدارة التبرعات\',
    \'reports\' => \'التقارير والإحصائيات\',
    \'backup\' => \'النسخ الاحتياطي\',
    \'settings\' => \'إعدادات النظام\'
]);

// إنشاء المجلدات المطلوبة إذا لم تكن موجودة
$required_dirs = [
    UPLOADS_PATH,
    RECEIPTS_PATH,
    BACKUPS_PATH,
    dirname(ERROR_LOG_FILE)
];

foreach ($required_dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// علامة التثبيت المكتمل
define(\'SYSTEM_INSTALLED\', true);
?>';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد مبسط - نظام إدارة التبرعات</title>
    
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .setup-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .setup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .setup-body {
            padding: 2rem;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1>إعداد مبسط</h1>
            <p>نظام إدارة التبرعات - جمعية قوافل الخير</p>
        </div>
        
        <div class="setup-body">
            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <?php foreach ($errors as $error): ?>
                        <div><i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?></div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($success)): ?>
                <div class="alert alert-success">
                    <?php foreach ($success as $msg): ?>
                        <div><i class="fas fa-check-circle me-2"></i><?php echo $msg; ?></div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($step == 1): ?>
                <h3>إعداد قاعدة البيانات</h3>
                
                <form method="POST" action="?step=1">
                    <div class="mb-3">
                        <label for="db_host" class="form-label">خادم قاعدة البيانات</label>
                        <input type="text" class="form-control" id="db_host" name="db_host" 
                               value="localhost" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="db_name" class="form-label">اسم قاعدة البيانات</label>
                        <input type="text" class="form-control" id="db_name" name="db_name" 
                               value="qawafil_donations" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="db_user" class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" id="db_user" name="db_user" 
                               value="root" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="db_pass" class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" id="db_pass" name="db_pass">
                    </div>
                    
                    <div class="mb-3">
                        <label for="system_url" class="form-label">رابط النظام</label>
                        <input type="url" class="form-control" id="system_url" name="system_url" 
                               value="http://localhost/mosra-kher" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-database me-2"></i>
                        إعداد النظام
                    </button>
                </form>
                
            <?php elseif ($step == 2): ?>
                <div class="text-center">
                    <i class="fas fa-check-circle fa-5x text-success mb-4"></i>
                    <h3 class="text-success">تم إعداد النظام بنجاح!</h3>
                    <p class="mb-4">يمكنك الآن البدء في استخدام النظام</p>
                    
                    <div class="d-grid gap-2">
                        <a href="admin/login.php" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            دخول الإدارة
                        </a>
                        <a href="public/index.php" class="btn btn-success">
                            <i class="fas fa-globe me-2"></i>
                            الموقع العام
                        </a>
                    </div>
                    
                    <div class="alert alert-info mt-4">
                        <strong>بيانات الدخول الافتراضية:</strong><br>
                        المدير: admin / admin123<br>
                        الموظف: employee1 / admin123
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
