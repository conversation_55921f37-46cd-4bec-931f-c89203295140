<?php
/**
 * نظام المصادقة والصلاحيات
 * جمعية قوافل الخير
 */

// منع الوصول المباشر
if (!defined('SYSTEM_ACCESS')) {
    die('Access denied');
}

class Auth {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * تسجيل الدخول
     */
    public function login($username, $password, $remember = false) {
        $user = $this->db->selectOne(
            "SELECT * FROM users WHERE username = :username AND status = 'active'",
            ['username' => $username]
        );
        
        if ($user && verifyPassword($password, $user['password'])) {
            $this->createSession($user);
            
            // تسجيل عملية الدخول
            logActivity('login');
            
            // تحديث آخر دخول
            $this->db->update('users', 
                ['updated_at' => date(DATETIME_FORMAT)], 
                'id = :id', 
                ['id' => $user['id']]
            );
            
            return ['success' => true, 'user' => $user];
        }
        
        return ['success' => false, 'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'];
    }
    
    /**
     * إنشاء جلسة المستخدم
     */
    private function createSession($user) {
        session_regenerate_id(true);
        
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['full_name'] = $user['full_name'];
        $_SESSION['role'] = $user['role'];
        $_SESSION['permissions'] = $this->parsePermissions($user['permissions']);
        $_SESSION['login_time'] = time();
        $_SESSION['last_activity'] = time();
    }
    
    /**
     * تحليل الصلاحيات
     */
    private function parsePermissions($permissions) {
        if ($permissions === 'all') {
            return array_keys(PERMISSIONS);
        }
        
        return $permissions ? explode(',', $permissions) : [];
    }
    
    /**
     * تسجيل الخروج
     */
    public function logout() {
        logActivity('logout');
        
        session_destroy();
        session_start();
        session_regenerate_id(true);
        
        return true;
    }
    
    /**
     * التحقق من تسجيل الدخول
     */
    public function isLoggedIn() {
        return isset($_SESSION['user_id']) && $this->isSessionValid();
    }
    
    /**
     * التحقق من صحة الجلسة
     */
    private function isSessionValid() {
        // التحقق من انتهاء الجلسة
        if (isset($_SESSION['last_activity']) && 
            (time() - $_SESSION['last_activity']) > SESSION_LIFETIME) {
            $this->logout();
            return false;
        }
        
        // تحديث آخر نشاط
        $_SESSION['last_activity'] = time();
        
        return true;
    }
    
    /**
     * التحقق من الصلاحية
     */
    public function hasPermission($permission) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        // المدير له جميع الصلاحيات
        if ($_SESSION['role'] === 'admin') {
            return true;
        }
        
        return in_array($permission, $_SESSION['permissions']);
    }
    
    /**
     * التحقق من الدور
     */
    public function hasRole($role) {
        return $this->isLoggedIn() && $_SESSION['role'] === $role;
    }
    
    /**
     * الحصول على المستخدم الحالي
     */
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        return $this->db->selectOne(
            "SELECT * FROM users WHERE id = :id",
            ['id' => $_SESSION['user_id']]
        );
    }
    
    /**
     * تغيير كلمة المرور
     */
    public function changePassword($userId, $oldPassword, $newPassword) {
        $user = $this->db->selectOne(
            "SELECT password FROM users WHERE id = :id",
            ['id' => $userId]
        );
        
        if (!$user || !verifyPassword($oldPassword, $user['password'])) {
            return ['success' => false, 'message' => 'كلمة المرور الحالية غير صحيحة'];
        }
        
        $hashedPassword = hashPassword($newPassword);
        $updated = $this->db->update('users', 
            ['password' => $hashedPassword], 
            'id = :id', 
            ['id' => $userId]
        );
        
        if ($updated) {
            logActivity('change_password', 'users', $userId);
            return ['success' => true, 'message' => 'تم تغيير كلمة المرور بنجاح'];
        }
        
        return ['success' => false, 'message' => 'فشل في تغيير كلمة المرور'];
    }
    
    /**
     * إنشاء مستخدم جديد
     */
    public function createUser($data) {
        // التحقق من عدم وجود اسم المستخدم
        if ($this->db->exists('users', 'username = :username', ['username' => $data['username']])) {
            return ['success' => false, 'message' => 'اسم المستخدم موجود مسبقاً'];
        }
        
        // تشفير كلمة المرور
        $data['password'] = hashPassword($data['password']);
        
        // إضافة تاريخ الإنشاء
        $data['created_at'] = date(DATETIME_FORMAT);
        
        $userId = $this->db->insert('users', $data);
        
        if ($userId) {
            logActivity('create_user', 'users', $userId, null, $data);
            return ['success' => true, 'user_id' => $userId];
        }
        
        return ['success' => false, 'message' => 'فشل في إنشاء المستخدم'];
    }
    
    /**
     * تحديث المستخدم
     */
    public function updateUser($userId, $data) {
        // إزالة كلمة المرور إذا كانت فارغة
        if (isset($data['password']) && empty($data['password'])) {
            unset($data['password']);
        } else if (isset($data['password'])) {
            $data['password'] = hashPassword($data['password']);
        }
        
        $data['updated_at'] = date(DATETIME_FORMAT);
        
        $updated = $this->db->update('users', $data, 'id = :id', ['id' => $userId]);
        
        if ($updated) {
            logActivity('update_user', 'users', $userId, null, $data);
            return ['success' => true];
        }
        
        return ['success' => false, 'message' => 'فشل في تحديث المستخدم'];
    }
    
    /**
     * حذف المستخدم
     */
    public function deleteUser($userId) {
        // منع حذف المدير الرئيسي
        if ($userId == 1) {
            return ['success' => false, 'message' => 'لا يمكن حذف المدير الرئيسي'];
        }
        
        $deleted = $this->db->delete('users', 'id = :id', ['id' => $userId]);
        
        if ($deleted) {
            logActivity('delete_user', 'users', $userId);
            return ['success' => true];
        }
        
        return ['success' => false, 'message' => 'فشل في حذف المستخدم'];
    }
    
    /**
     * الحصول على جميع المستخدمين
     */
    public function getUsers($limit = null, $offset = 0) {
        $sql = "SELECT id, username, full_name, email, phone, role, status, created_at FROM users ORDER BY created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT $limit OFFSET $offset";
        }
        
        return $this->db->select($sql);
    }
    
    /**
     * عدد المستخدمين
     */
    public function getUsersCount() {
        return $this->db->count('users');
    }
    
    /**
     * البحث في المستخدمين
     */
    public function searchUsers($query, $limit = null, $offset = 0) {
        $sql = "SELECT id, username, full_name, email, phone, role, status, created_at 
                FROM users 
                WHERE username LIKE :query OR full_name LIKE :query OR email LIKE :query
                ORDER BY created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT $limit OFFSET $offset";
        }
        
        $searchQuery = "%$query%";
        return $this->db->select($sql, ['query' => $searchQuery]);
    }
}

/**
 * دوال مساعدة للمصادقة
 */

// إنشاء كائن المصادقة العام
$auth = new Auth();

/**
 * التحقق من تسجيل الدخول وإعادة التوجيه
 */
function requireLogin($redirectUrl = 'login.php') {
    global $auth;
    
    if (!$auth->isLoggedIn()) {
        redirect($redirectUrl, 'يجب تسجيل الدخول أولاً', 'warning');
    }
}

/**
 * التحقق من الصلاحية وإعادة التوجيه
 */
function requirePermission($permission, $redirectUrl = 'index.php') {
    global $auth;
    
    requireLogin();
    
    if (!$auth->hasPermission($permission)) {
        redirect($redirectUrl, 'ليس لديك صلاحية للوصول لهذه الصفحة', 'error');
    }
}

/**
 * التحقق من الدور وإعادة التوجيه
 */
function requireRole($role, $redirectUrl = 'index.php') {
    global $auth;
    
    requireLogin();
    
    if (!$auth->hasRole($role)) {
        redirect($redirectUrl, 'ليس لديك صلاحية للوصول لهذه الصفحة', 'error');
    }
}

/**
 * بدء الجلسة
 */
function startSession() {
    if (session_status() === PHP_SESSION_NONE) {
        session_name(SESSION_NAME);
        session_start();
    }
}

// بدء الجلسة تلقائياً
startSession();
?>
