<?php
/**
 * صفحة إدارة التبرعات المحدثة
 * جمعية قوافل الخير
 */

define('SYSTEM_ACCESS', true);
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// التحقق من الصلاحية
requirePermission('donations');

$action = $_GET['action'] ?? 'list';
$donationId = $_GET['id'] ?? null;

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'create') {
        try {
            $donationType = sanitize($_POST['donation_type']);

            // البيانات المشتركة
            $commonData = [
                'donation_type' => $donationType,
                'amount' => floatval($_POST['amount']),
                'currency' => sanitize($_POST['currency']) ?: 'DZD',
                'payment_method' => sanitize($_POST['payment_method']),
                'registration_number' => sanitize($_POST['registration_number']),
                'withdrawal_number' => sanitize($_POST['withdrawal_number']),
                'expense_voucher' => sanitize($_POST['expense_voucher']),
                'donation_date' => sanitize($_POST['donation_date']),
                'serial_number' => generateSerialNumber(),
                'notes' => sanitize($_POST['notes']),
                'created_by' => $_SESSION['user_id']
            ];

            // البيانات الخاصة بكل نوع
            if ($donationType === 'monthly_sponsorship') {
                $specificData = [
                    'sponsor_name' => sanitize($_POST['sponsor_name']),
                    'sponsor_gender' => sanitize($_POST['sponsor_gender']),
                    'sponsor_phone' => sanitize($_POST['sponsor_phone']),
                    'sponsor_state' => sanitize($_POST['sponsor_state']),
                    'monthly_amount' => floatval($_POST['monthly_amount']),
                    'family_number' => sanitize($_POST['family_number']),
                    'sponsorship_month' => intval($_POST['sponsorship_month']),
                    'sponsorship_year' => intval($_POST['sponsorship_year'])
                ];
            } elseif ($donationType === 'restricted') {
                $specificData = [
                    'donor_name' => sanitize($_POST['donor_name']),
                    'restriction_type' => sanitize($_POST['restriction_type']),
                    'family_file_number' => sanitize($_POST['family_file_number']),
                    'project_type' => sanitize($_POST['project_type'])
                ];
            } else { // unrestricted
                $specificData = [
                    'donor_name' => sanitize($_POST['donor_name'])
                ];
            }

            $donationData = array_merge($commonData, $specificData);
            $donationId = $db->insert('donations', $donationData);

            if ($donationId) {
                logActivity('create_donation', 'donations', $donationId, null, $donationData);
                redirect('donations.php', 'تم إضافة التبرع بنجاح', 'success');
            } else {
                throw new Exception('فشل في إضافة التبرع');
            }

        } catch (Exception $e) {
            setFlashMessage('خطأ في إضافة التبرع: ' . $e->getMessage(), 'error');
        }
    }
}

// حذف التبرع
if ($action === 'delete' && $donationId) {
    $deleted = $db->delete('donations', 'id = :id', ['id' => $donationId]);

    if ($deleted) {
        logActivity('delete_donation', 'donations', $donationId);
        redirect('donations.php', 'تم حذف التبرع بنجاح', 'success');
    } else {
        redirect('donations.php', 'فشل في حذف التبرع', 'error');
    }
}

// قائمة التبرعات مع البحث والفلترة
$page = intval($_GET['page'] ?? 1);
$limit = 25;
$offset = ($page - 1) * $limit;

$searchQuery = sanitize($_GET['search'] ?? '');
$typeFilter = sanitize($_GET['type'] ?? '');
$dateFrom = sanitize($_GET['date_from'] ?? '');
$dateTo = sanitize($_GET['date_to'] ?? '');

$whereConditions = ['1=1'];
$params = [];

if ($searchQuery) {
    $whereConditions[] = "(sponsor_name LIKE :search OR donor_name LIKE :search OR serial_number LIKE :search OR family_number LIKE :search)";
    $params['search'] = "%$searchQuery%";
}

if ($typeFilter) {
    $whereConditions[] = "donation_type = :type";
    $params['type'] = $typeFilter;
}

if ($dateFrom) {
    $whereConditions[] = "donation_date >= :date_from";
    $params['date_from'] = $dateFrom;
}

if ($dateTo) {
    $whereConditions[] = "donation_date <= :date_to";
    $params['date_to'] = $dateTo;
}

$whereClause = implode(' AND ', $whereConditions);

// عدد التبرعات
$totalDonations = $db->selectOne("
    SELECT COUNT(*) as count
    FROM donations
    WHERE $whereClause
", $params)['count'];

$totalPages = ceil($totalDonations / $limit);

// قائمة التبرعات
$donations = $db->select("
    SELECT d.*, u.full_name as created_by_name
    FROM donations d
    LEFT JOIN users u ON d.created_by = u.id
    WHERE $whereClause
    ORDER BY d.created_at DESC
    LIMIT $limit OFFSET $offset
", $params);

$pageTitle = 'إدارة التبرعات';
include 'includes/header.php';
?>

<?php if ($action === 'list'): ?>
    <!-- فلاتر البحث -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">البحث والفلترة</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="search">البحث</label>
                            <input type="text" class="form-control" id="search" name="search"
                                   value="<?php echo htmlspecialchars($searchQuery); ?>"
                                   placeholder="اسم الكافل، المتبرع، رقم العائلة، السيريال">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="type">نوع التبرع</label>
                            <select class="form-control" id="type" name="type">
                                <option value="">الكل</option>
                                <?php foreach (DONATION_TYPES as $key => $value): ?>
                                    <option value="<?php echo $key; ?>"
                                            <?php echo $typeFilter === $key ? 'selected' : ''; ?>>
                                        <?php echo $value; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_from">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from"
                                   value="<?php echo $dateFrom; ?>">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_to">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to"
                                   value="<?php echo $dateTo; ?>">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div class="d-flex">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                                <a href="donations.php" class="btn btn-secondary me-2">
                                    <i class="fas fa-refresh"></i> إعادة تعيين
                                </a>
                                <a href="donations.php?action=create" class="btn btn-success">
                                    <i class="fas fa-plus"></i> تبرع جديد
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- قائمة التبرعات -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                قائمة التبرعات (<?php echo number_format($totalDonations); ?> تبرع)
            </h6>
            <div>
                <button onclick="exportToCSV('donationsTable', 'donations.csv')" class="btn btn-success btn-sm">
                    <i class="fas fa-file-excel"></i> تصدير Excel
                </button>
                <button onclick="printPage()" class="btn btn-info btn-sm">
                    <i class="fas fa-print"></i> طباعة
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="donationsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>الرقم التسلسلي</th>
                            <th>النوع</th>
                            <th>الاسم</th>
                            <th>المبلغ</th>
                            <th>طريقة الدفع</th>
                            <th>التاريخ</th>
                            <th>المسؤول</th>
                            <th>العمليات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($donations as $donation): ?>
                            <tr>
                                <td>
                                    <strong><?php echo $donation['serial_number']; ?></strong>
                                    <?php if ($donation['registration_number']): ?>
                                        <br><small class="text-muted">رقم التسجيل: <?php echo $donation['registration_number']; ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge badge-<?php
                                        echo $donation['donation_type'] === 'monthly_sponsorship' ? 'primary' :
                                            ($donation['donation_type'] === 'restricted' ? 'warning' : 'info');
                                    ?>">
                                        <?php echo DONATION_TYPES[$donation['donation_type']]; ?>
                                    </span>
                                </td>
                                <td>
                                    <?php
                                    $name = $donation['sponsor_name'] ?: $donation['donor_name'];
                                    echo htmlspecialchars($name);
                                    ?>
                                    <?php if ($donation['sponsor_phone']): ?>
                                        <br><small class="text-muted"><?php echo $donation['sponsor_phone']; ?></small>
                                    <?php endif; ?>
                                    <?php if ($donation['family_number']): ?>
                                        <br><small class="text-info">عائلة: <?php echo $donation['family_number']; ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong><?php echo number_format($donation['amount'], 2); ?> <?php echo $donation['currency']; ?></strong>
                                    <?php if ($donation['monthly_amount']): ?>
                                        <br><small class="text-muted">شهري: <?php echo number_format($donation['monthly_amount'], 2); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge badge-<?php echo $donation['payment_method'] === 'cash' ? 'success' : 'primary'; ?>">
                                        <?php echo PAYMENT_METHODS[$donation['payment_method']]; ?>
                                    </span>
                                </td>
                                <td><?php echo formatDate($donation['donation_date']); ?></td>
                                <td><?php echo htmlspecialchars($donation['created_by_name']); ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="donations.php?action=view&id=<?php echo $donation['id']; ?>"
                                           class="btn btn-sm btn-info" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="donations.php?action=edit&id=<?php echo $donation['id']; ?>"
                                           class="btn btn-sm btn-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="donations.php?action=delete&id=<?php echo $donation['id']; ?>"
                                           class="btn btn-sm btn-danger" title="حذف"
                                           onclick="return confirmDelete('هل أنت متأكد من حذف هذا التبرع؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <div class="d-flex justify-content-center mt-3">
                    <?php
                    $queryParams = $_GET;
                    echo createPagination($page, $totalPages, 'donations.php', $queryParams);
                    ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php elseif ($action === 'create'): ?>
    <!-- نموذج إضافة تبرع جديد -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">إضافة تبرع جديد</h6>
        </div>
        <div class="card-body">
            <!-- اختيار نوع التبرع -->
            <div class="row mb-4">
                <div class="col-12">
                    <h5>اختر نوع التبرع:</h5>
                    <div class="btn-group w-100" role="group">
                        <input type="radio" class="btn-check" name="donation_type_selector" id="monthly_sponsorship" value="monthly_sponsorship" checked>
                        <label class="btn btn-outline-primary" for="monthly_sponsorship">
                            <i class="fas fa-hand-holding-heart me-2"></i>
                            الكفالات الشهرية
                        </label>

                        <input type="radio" class="btn-check" name="donation_type_selector" id="restricted" value="restricted">
                        <label class="btn btn-outline-warning" for="restricted">
                            <i class="fas fa-project-diagram me-2"></i>
                            التبرعات المقيدة
                        </label>

                        <input type="radio" class="btn-check" name="donation_type_selector" id="unrestricted" value="unrestricted">
                        <label class="btn btn-outline-info" for="unrestricted">
                            <i class="fas fa-hands-helping me-2"></i>
                            التبرعات غير المقيدة
                        </label>
                    </div>
                </div>
            </div>

            <form method="POST" action="?action=create" id="donationForm">
                <input type="hidden" name="donation_type" id="donation_type" value="monthly_sponsorship">

                <!-- نموذج الكفالات الشهرية -->
                <div id="monthly_sponsorship_form" class="donation-form">
                    <h5 class="text-primary mb-3">
                        <i class="fas fa-hand-holding-heart me-2"></i>
                        بيانات الكفالة الشهرية
                    </h5>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="sponsor_name" class="form-label">اسم الكافل *</label>
                                <input type="text" class="form-control" id="sponsor_name" name="sponsor_name" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <label for="sponsor_gender" class="form-label">الجنس *</label>
                                <select class="form-control" id="sponsor_gender" name="sponsor_gender" required>
                                    <option value="">-- اختر --</option>
                                    <?php foreach (GENDER_TYPES as $key => $value): ?>
                                        <option value="<?php echo $key; ?>"><?php echo $value; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <label for="sponsor_phone" class="form-label">رقم الهاتف</label>
                                <input type="text" class="form-control" id="sponsor_phone" name="sponsor_phone">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label for="sponsor_state" class="form-label">الولاية</label>
                                <select class="form-control" id="sponsor_state" name="sponsor_state">
                                    <option value="">-- اختر الولاية --</option>
                                    <?php foreach (ALGERIAN_STATES as $code => $name): ?>
                                        <option value="<?php echo $name; ?>"><?php echo $code . ' - ' . $name; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label for="monthly_amount" class="form-label">القيمة الشهرية *</label>
                                <input type="number" class="form-control" id="monthly_amount" name="monthly_amount"
                                       step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label for="family_number" class="form-label">رقم العائلة *</label>
                                <input type="text" class="form-control" id="family_number" name="family_number" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="sponsorship_month" class="form-label">الشهر *</label>
                                <select class="form-control" id="sponsorship_month" name="sponsorship_month" required>
                                    <option value="">-- اختر الشهر --</option>
                                    <?php for ($i = 1; $i <= 12; $i++): ?>
                                        <option value="<?php echo $i; ?>" <?php echo date('n') == $i ? 'selected' : ''; ?>>
                                            <?php echo $i; ?>
                                        </option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="sponsorship_year" class="form-label">السنة *</label>
                                <select class="form-control" id="sponsorship_year" name="sponsorship_year" required>
                                    <option value="">-- اختر السنة --</option>
                                    <?php for ($year = date('Y') - 1; $year <= date('Y') + 1; $year++): ?>
                                        <option value="<?php echo $year; ?>" <?php echo date('Y') == $year ? 'selected' : ''; ?>>
                                            <?php echo $year; ?>
                                        </option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- نموذج التبرعات المقيدة -->
                <div id="restricted_form" class="donation-form" style="display: none;">
                    <h5 class="text-warning mb-3">
                        <i class="fas fa-project-diagram me-2"></i>
                        بيانات التبرع المقيد
                    </h5>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="donor_name_restricted" class="form-label">المتبرع(ة) *</label>
                                <input type="text" class="form-control" id="donor_name_restricted" name="donor_name">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="restriction_type" class="form-label">نوع التقييد *</label>
                                <select class="form-control" id="restriction_type" name="restriction_type">
                                    <option value="">-- اختر نوع التقييد --</option>
                                    <?php foreach (RESTRICTION_TYPES as $key => $value): ?>
                                        <option value="<?php echo $key; ?>"><?php echo $value; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6" id="family_restriction_field" style="display: none;">
                            <div class="form-group mb-3">
                                <label for="family_file_number" class="form-label">رقم ملف العائلة</label>
                                <input type="text" class="form-control" id="family_file_number" name="family_file_number">
                            </div>
                        </div>
                        <div class="col-md-6" id="project_restriction_field" style="display: none;">
                            <div class="form-group mb-3">
                                <label for="project_type" class="form-label">المشروع</label>
                                <select class="form-control" id="project_type" name="project_type">
                                    <option value="">-- اختر المشروع --</option>
                                    <?php foreach (PROJECT_TYPES as $key => $value): ?>
                                        <option value="<?php echo $key; ?>"><?php echo $value; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- نموذج التبرعات غير المقيدة -->
                <div id="unrestricted_form" class="donation-form" style="display: none;">
                    <h5 class="text-info mb-3">
                        <i class="fas fa-hands-helping me-2"></i>
                        بيانات التبرع غير المقيد
                    </h5>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="donor_name_unrestricted" class="form-label">المتبرع(ة) *</label>
                                <input type="text" class="form-control" id="donor_name_unrestricted" name="donor_name">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- البيانات المشتركة -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">البيانات المشتركة</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="amount" class="form-label">القيمة *</label>
                                    <input type="number" class="form-control" id="amount" name="amount"
                                           step="0.01" min="0" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="donation_date" class="form-label">التاريخ *</label>
                                    <input type="date" class="form-control" id="donation_date" name="donation_date"
                                           value="<?php echo date('Y-m-d'); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="currency" class="form-label">العملة</label>
                                    <select class="form-control" id="currency" name="currency">
                                        <option value="DZD">دينار جزائري</option>
                                        <option value="USD">دولار أمريكي</option>
                                        <option value="EUR">يورو</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="payment_method" class="form-label">طريقة الدفع *</label>
                                    <select class="form-control" id="payment_method" name="payment_method" required>
                                        <?php foreach (PAYMENT_METHODS as $key => $value): ?>
                                            <option value="<?php echo $key; ?>"><?php echo $value; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="registration_number" class="form-label">رقم تسجيل القيمة</label>
                                    <input type="text" class="form-control" id="registration_number" name="registration_number">
                                    <small class="form-text text-muted">
                                        <span class="text-success">■</span> نقدي: أخضر فاتح |
                                        <span class="text-primary">■</span> عبر الحساب: أزرق فاتح
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row" id="bank_fields" style="display: none;">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="withdrawal_number" class="form-label">رقم السحب</label>
                                    <input type="text" class="form-control" id="withdrawal_number" name="withdrawal_number">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="expense_voucher" class="form-label">سند الصرف</label>
                                    <select class="form-control" id="expense_voucher" name="expense_voucher">
                                        <option value="">-- سيتم ملؤه لاحقاً --</option>
                                        <?php for ($i = 1; $i <= 50000; $i++): ?>
                                            <option value="<?php echo str_pad($i, 5, '0', STR_PAD_LEFT); ?>">
                                                <?php echo str_pad($i, 5, '0', STR_PAD_LEFT); ?>
                                            </option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="notes" class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ التبرع
                    </button>
                    <a href="donations.php" class="btn btn-secondary">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>

<script>
// التحكم في نوع التبرع
document.querySelectorAll('input[name="donation_type_selector"]').forEach(function(radio) {
    radio.addEventListener('change', function() {
        // إخفاء جميع النماذج
        document.querySelectorAll('.donation-form').forEach(form => form.style.display = 'none');

        // إظهار النموذج المحدد
        document.getElementById(this.value + '_form').style.display = 'block';

        // تحديث قيمة نوع التبرع المخفية
        document.getElementById('donation_type').value = this.value;

        // تحديث الحقول المطلوبة
        updateRequiredFields(this.value);
    });
});

// التحكم في عرض حقول البنك
document.getElementById('payment_method').addEventListener('change', function() {
    const bankFields = document.getElementById('bank_fields');
    const registrationInput = document.getElementById('registration_number');

    if (this.value === 'bank_account') {
        bankFields.style.display = 'block';
        registrationInput.style.backgroundColor = '#ADD8E6'; // أزرق فاتح
    } else {
        bankFields.style.display = 'none';
        registrationInput.style.backgroundColor = '#90EE90'; // أخضر فاتح
    }
});

// التحكم في نوع التقييد
document.getElementById('restriction_type').addEventListener('change', function() {
    const familyField = document.getElementById('family_restriction_field');
    const projectField = document.getElementById('project_restriction_field');

    familyField.style.display = 'none';
    projectField.style.display = 'none';

    if (this.value === 'family') {
        familyField.style.display = 'block';
    } else if (this.value === 'project') {
        projectField.style.display = 'block';
    }
});

// تحديث الحقول المطلوبة
function updateRequiredFields(donationType) {
    // إزالة required من جميع الحقول
    document.querySelectorAll('.donation-form input, .donation-form select').forEach(field => {
        field.removeAttribute('required');
    });

    // إضافة required للحقول المناسبة
    if (donationType === 'monthly_sponsorship') {
        document.getElementById('sponsor_name').setAttribute('required', 'required');
        document.getElementById('sponsor_gender').setAttribute('required', 'required');
        document.getElementById('monthly_amount').setAttribute('required', 'required');
        document.getElementById('family_number').setAttribute('required', 'required');
        document.getElementById('sponsorship_month').setAttribute('required', 'required');
        document.getElementById('sponsorship_year').setAttribute('required', 'required');
    } else if (donationType === 'restricted') {
        document.getElementById('donor_name_restricted').setAttribute('required', 'required');
    } else if (donationType === 'unrestricted') {
        document.getElementById('donor_name_unrestricted').setAttribute('required', 'required');
    }
}

// تشغيل الدوال عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تشغيل تغيير طريقة الدفع
    document.getElementById('payment_method').dispatchEvent(new Event('change'));

    // تحديث الحقول المطلوبة للنوع الافتراضي
    updateRequiredFields('monthly_sponsorship');
});
</script>

<?php endif; ?>

<?php include 'includes/footer.php'; ?>
