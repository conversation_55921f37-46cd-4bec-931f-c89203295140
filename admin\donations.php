<?php
/**
 * صفحة إدارة التبرعات
 * جمعية قوافل الخير
 */

define('SYSTEM_ACCESS', true);
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// التحقق من الصلاحية
requirePermission('donations');

$action = $_GET['action'] ?? 'list';
$donationId = $_GET['id'] ?? null;

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'create') {
        // البحث عن المتبرع أو إنشاء جديد
        $donorId = null;
        
        if (!empty($_POST['existing_donor_id'])) {
            $donorId = $_POST['existing_donor_id'];
        } else {
            // إنشاء متبرع جديد
            $donorData = [
                'name' => sanitize($_POST['donor_name']),
                'phone' => sanitize($_POST['donor_phone']),
                'email' => sanitize($_POST['donor_email']),
                'address' => sanitize($_POST['donor_address']),
                'national_id' => sanitize($_POST['donor_national_id']),
                'donor_code' => generateDonorCode()
            ];
            
            $donorId = $db->insert('donors', $donorData);
        }
        
        if ($donorId) {
            // إنشاء التبرع
            $donationData = [
                'donor_id' => $donorId,
                'donation_type' => sanitize($_POST['donation_type']),
                'amount' => floatval($_POST['amount']),
                'currency' => sanitize($_POST['currency']),
                'receipt_number' => sanitize($_POST['receipt_number']),
                'serial_number' => generateSerialNumber(),
                'project_name' => sanitize($_POST['project_name']),
                'beneficiary_family' => sanitize($_POST['beneficiary_family']),
                'sponsorship_duration' => intval($_POST['sponsorship_duration']),
                'payment_method' => sanitize($_POST['payment_method']),
                'notes' => sanitize($_POST['notes']),
                'created_by' => $_SESSION['user_id']
            ];
            
            $donationId = $db->insert('donations', $donationData);
            
            if ($donationId) {
                logActivity('create_donation', 'donations', $donationId, null, $donationData);
                redirect('donations.php?action=receipt&id=' . $donationId, 'تم إضافة التبرع بنجاح', 'success');
            }
        }
        
        setFlashMessage('فشل في إضافة التبرع', 'error');
    }
}

// حذف التبرع
if ($action === 'delete' && $donationId) {
    $deleted = $db->delete('donations', 'id = :id', ['id' => $donationId]);
    
    if ($deleted) {
        logActivity('delete_donation', 'donations', $donationId);
        redirect('donations.php', 'تم حذف التبرع بنجاح', 'success');
    } else {
        redirect('donations.php', 'فشل في حذف التبرع', 'error');
    }
}

// الحصول على البيانات
if ($action === 'edit' && $donationId) {
    $donation = $db->selectOne("
        SELECT d.*, don.name as donor_name, don.phone as donor_phone, 
               don.email as donor_email, don.address as donor_address,
               don.national_id as donor_national_id
        FROM donations d
        LEFT JOIN donors don ON d.donor_id = don.id
        WHERE d.id = :id
    ", ['id' => $donationId]);
    
    if (!$donation) {
        redirect('donations.php', 'التبرع غير موجود', 'error');
    }
}

// قائمة التبرعات مع البحث والفلترة
$page = intval($_GET['page'] ?? 1);
$limit = 25;
$offset = ($page - 1) * $limit;

$searchQuery = sanitize($_GET['search'] ?? '');
$typeFilter = sanitize($_GET['type'] ?? '');
$dateFrom = sanitize($_GET['date_from'] ?? '');
$dateTo = sanitize($_GET['date_to'] ?? '');

$whereConditions = ['1=1'];
$params = [];

if ($searchQuery) {
    $whereConditions[] = "(don.name LIKE :search OR d.serial_number LIKE :search OR d.receipt_number LIKE :search)";
    $params['search'] = "%$searchQuery%";
}

if ($typeFilter) {
    $whereConditions[] = "d.donation_type = :type";
    $params['type'] = $typeFilter;
}

if ($dateFrom) {
    $whereConditions[] = "DATE(d.created_at) >= :date_from";
    $params['date_from'] = $dateFrom;
}

if ($dateTo) {
    $whereConditions[] = "DATE(d.created_at) <= :date_to";
    $params['date_to'] = $dateTo;
}

$whereClause = implode(' AND ', $whereConditions);

// عدد التبرعات
$totalDonations = $db->selectOne("
    SELECT COUNT(*) as count
    FROM donations d
    LEFT JOIN donors don ON d.donor_id = don.id
    WHERE $whereClause
", $params)['count'];

$totalPages = ceil($totalDonations / $limit);

// قائمة التبرعات
$donations = $db->select("
    SELECT d.*, don.name as donor_name, don.phone as donor_phone,
           u.full_name as created_by_name
    FROM donations d
    LEFT JOIN donors don ON d.donor_id = don.id
    LEFT JOIN users u ON d.created_by = u.id
    WHERE $whereClause
    ORDER BY d.created_at DESC
    LIMIT $limit OFFSET $offset
", $params);

// قائمة المتبرعين للاختيار
$donors = $db->select("SELECT id, name, phone FROM donors ORDER BY name");

$pageTitle = 'إدارة التبرعات';
include 'includes/header.php';
?>

<?php if ($action === 'list'): ?>
    <!-- فلاتر البحث -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">البحث والفلترة</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="search">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($searchQuery); ?>"
                                   placeholder="اسم المتبرع، رقم الإيصال، السيريال">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="type">نوع التبرع</label>
                            <select class="form-control" id="type" name="type">
                                <option value="">الكل</option>
                                <?php foreach (DONATION_TYPES as $key => $value): ?>
                                    <option value="<?php echo $key; ?>" 
                                            <?php echo $typeFilter === $key ? 'selected' : ''; ?>>
                                        <?php echo $value; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_from">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" 
                                   value="<?php echo $dateFrom; ?>">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_to">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" 
                                   value="<?php echo $dateTo; ?>">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div class="d-flex">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                                <a href="donations.php" class="btn btn-secondary me-2">
                                    <i class="fas fa-refresh"></i> إعادة تعيين
                                </a>
                                <a href="donations.php?action=create" class="btn btn-success">
                                    <i class="fas fa-plus"></i> تبرع جديد
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- قائمة التبرعات -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                قائمة التبرعات (<?php echo number_format($totalDonations); ?> تبرع)
            </h6>
            <div>
                <button onclick="exportToCSV('donationsTable', 'donations.csv')" class="btn btn-success btn-sm">
                    <i class="fas fa-file-excel"></i> تصدير Excel
                </button>
                <button onclick="printPage()" class="btn btn-info btn-sm">
                    <i class="fas fa-print"></i> طباعة
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="donationsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>الرقم التسلسلي</th>
                            <th>اسم المتبرع</th>
                            <th>نوع التبرع</th>
                            <th>المبلغ</th>
                            <th>طريقة الدفع</th>
                            <th>التاريخ</th>
                            <th>المسؤول</th>
                            <th>العمليات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($donations as $donation): ?>
                            <tr>
                                <td>
                                    <strong><?php echo $donation['serial_number']; ?></strong>
                                    <?php if ($donation['receipt_number']): ?>
                                        <br><small class="text-muted">إيصال: <?php echo $donation['receipt_number']; ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php echo htmlspecialchars($donation['donor_name']); ?>
                                    <?php if ($donation['donor_phone']): ?>
                                        <br><small class="text-muted"><?php echo $donation['donor_phone']; ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge badge-<?php 
                                        echo $donation['donation_type'] === 'monthly_sponsorship' ? 'primary' : 
                                            ($donation['donation_type'] === 'restricted' ? 'warning' : 'info'); 
                                    ?>">
                                        <?php echo DONATION_TYPES[$donation['donation_type']]; ?>
                                    </span>
                                    <?php if ($donation['project_name']): ?>
                                        <br><small><?php echo htmlspecialchars($donation['project_name']); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong><?php echo formatAmount($donation['amount'], $donation['currency']); ?></strong>
                                </td>
                                <td><?php echo PAYMENT_METHODS[$donation['payment_method']]; ?></td>
                                <td><?php echo formatDateTime($donation['created_at']); ?></td>
                                <td><?php echo htmlspecialchars($donation['created_by_name']); ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="donations.php?action=receipt&id=<?php echo $donation['id']; ?>" 
                                           class="btn btn-sm btn-primary" title="طباعة الإيصال">
                                            <i class="fas fa-receipt"></i>
                                        </a>
                                        <a href="donations.php?action=edit&id=<?php echo $donation['id']; ?>" 
                                           class="btn btn-sm btn-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="donations.php?action=delete&id=<?php echo $donation['id']; ?>" 
                                           class="btn btn-sm btn-danger" title="حذف"
                                           onclick="return confirmDelete('هل أنت متأكد من حذف هذا التبرع؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <div class="d-flex justify-content-center mt-3">
                    <?php
                    $queryParams = $_GET;
                    echo createPagination($page, $totalPages, 'donations.php', $queryParams);
                    ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

<?php elseif ($action === 'receipt' && $donationId): ?>
    <!-- عرض/تحميل الإيصال -->
    <?php
    require_once '../includes/pdf_generator.php';
    
    $donation = $db->selectOne("
        SELECT d.*, don.name as donor_name
        FROM donations d
        LEFT JOIN donors don ON d.donor_id = don.id
        WHERE d.id = :id
    ", ['id' => $donationId]);
    
    if (!$donation) {
        redirect('donations.php', 'التبرع غير موجود', 'error');
    }
    
    $pdfGenerator = new PDFGenerator();
    
    // توليد الإيصال إذا لم يكن موجوداً
    if (!$donation['receipt_path'] || !file_exists(RECEIPTS_PATH . '/' . $donation['receipt_path'])) {
        $receiptFile = $pdfGenerator->generateDonationReceipt($donationId);
        if (!$receiptFile) {
            redirect('donations.php', 'فشل في توليد الإيصال', 'error');
        }
    } else {
        $receiptFile = $donation['receipt_path'];
    }
    
    // تحميل الملف
    $filePath = RECEIPTS_PATH . '/' . $receiptFile;
    if (file_exists($filePath)) {
        header('Content-Type: application/pdf');
        header('Content-Disposition: inline; filename="' . $receiptFile . '"');
        header('Content-Length: ' . filesize($filePath));
        readfile($filePath);
        exit;
    } else {
        redirect('donations.php', 'ملف الإيصال غير موجود', 'error');
    }
    ?>

<?php elseif ($action === 'create'): ?>
    <!-- نموذج إضافة تبرع جديد -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">إضافة تبرع جديد</h6>
        </div>
        <div class="card-body">
            <form method="POST" action="" id="donationForm">
                <!-- بيانات المتبرع -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">بيانات المتبرع</h6>
                    </div>
                    <div class="card-body">
                        <div class="form-group mb-3">
                            <label class="form-label">اختيار المتبرع</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="donor_option"
                                       id="existing_donor" value="existing" checked>
                                <label class="form-check-label" for="existing_donor">
                                    متبرع موجود
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="donor_option"
                                       id="new_donor" value="new">
                                <label class="form-check-label" for="new_donor">
                                    متبرع جديد
                                </label>
                            </div>
                        </div>

                        <div id="existing_donor_section">
                            <div class="form-group mb-3">
                                <label for="existing_donor_id" class="form-label">اختر المتبرع</label>
                                <select class="form-control" id="existing_donor_id" name="existing_donor_id">
                                    <option value="">-- اختر المتبرع --</option>
                                    <?php foreach ($donors as $donor): ?>
                                        <option value="<?php echo $donor['id']; ?>">
                                            <?php echo htmlspecialchars($donor['name']); ?>
                                            <?php if ($donor['phone']): ?>
                                                - <?php echo $donor['phone']; ?>
                                            <?php endif; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div id="new_donor_section" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="donor_name" class="form-label">اسم المتبرع *</label>
                                        <input type="text" class="form-control" id="donor_name" name="donor_name">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="donor_phone" class="form-label">رقم الهاتف</label>
                                        <input type="text" class="form-control" id="donor_phone" name="donor_phone">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="donor_email" class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" id="donor_email" name="donor_email">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="donor_national_id" class="form-label">رقم الهوية</label>
                                        <input type="text" class="form-control" id="donor_national_id" name="donor_national_id">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group mb-3">
                                <label for="donor_address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="donor_address" name="donor_address" rows="2"></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- بيانات التبرع -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">بيانات التبرع</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="donation_type" class="form-label">نوع التبرع *</label>
                                    <select class="form-control" id="donation_type" name="donation_type" required>
                                        <?php foreach (DONATION_TYPES as $key => $value): ?>
                                            <option value="<?php echo $key; ?>"><?php echo $value; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="amount" class="form-label">المبلغ *</label>
                                    <input type="number" class="form-control" id="amount" name="amount"
                                           step="0.01" min="0" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="currency" class="form-label">العملة</label>
                                    <select class="form-control" id="currency" name="currency">
                                        <option value="SAR">ريال سعودي</option>
                                        <option value="USD">دولار أمريكي</option>
                                        <option value="EUR">يورو</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="payment_method" class="form-label">طريقة الدفع *</label>
                                    <select class="form-control" id="payment_method" name="payment_method" required>
                                        <?php foreach (PAYMENT_METHODS as $key => $value): ?>
                                            <option value="<?php echo $key; ?>"><?php echo $value; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="receipt_number" class="form-label">رقم الإيصال</label>
                                    <input type="text" class="form-control" id="receipt_number" name="receipt_number">
                                    <small class="form-text text-muted">سيتم ملؤه يدوياً لاحقاً إذا ترك فارغاً</small>
                                </div>
                            </div>
                        </div>

                        <!-- حقول إضافية حسب نوع التبرع -->
                        <div id="restricted_fields" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="project_name" class="form-label">اسم المشروع</label>
                                        <input type="text" class="form-control" id="project_name" name="project_name">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="beneficiary_family" class="form-label">العائلة المستفيدة</label>
                                        <input type="text" class="form-control" id="beneficiary_family" name="beneficiary_family">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="sponsorship_fields" style="display: none;">
                            <div class="form-group mb-3">
                                <label for="sponsorship_duration" class="form-label">مدة الكفالة (بالأشهر)</label>
                                <input type="number" class="form-control" id="sponsorship_duration" name="sponsorship_duration" min="1">
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ التبرع وإنشاء الإيصال
                    </button>
                    <a href="donations.php" class="btn btn-secondary">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>

<script>
// التحكم في عرض حقول المتبرع
document.querySelectorAll('input[name="donor_option"]').forEach(function(radio) {
    radio.addEventListener('change', function() {
        const existingSection = document.getElementById('existing_donor_section');
        const newSection = document.getElementById('new_donor_section');

        if (this.value === 'existing') {
            existingSection.style.display = 'block';
            newSection.style.display = 'none';
            // إزالة required من حقول المتبرع الجديد
            newSection.querySelectorAll('[required]').forEach(field => field.removeAttribute('required'));
            // إضافة required لحقل المتبرع الموجود
            document.getElementById('existing_donor_id').setAttribute('required', 'required');
        } else {
            existingSection.style.display = 'none';
            newSection.style.display = 'block';
            // إضافة required لحقول المتبرع الجديد
            document.getElementById('donor_name').setAttribute('required', 'required');
            // إزالة required من حقل المتبرع الموجود
            document.getElementById('existing_donor_id').removeAttribute('required');
        }
    });
});

// التحكم في عرض الحقول حسب نوع التبرع
document.getElementById('donation_type').addEventListener('change', function() {
    const restrictedFields = document.getElementById('restricted_fields');
    const sponsorshipFields = document.getElementById('sponsorship_fields');

    // إخفاء جميع الحقول أولاً
    restrictedFields.style.display = 'none';
    sponsorshipFields.style.display = 'none';

    if (this.value === 'restricted') {
        restrictedFields.style.display = 'block';
    } else if (this.value === 'monthly_sponsorship') {
        sponsorshipFields.style.display = 'block';
        restrictedFields.style.display = 'block'; // يمكن أن تكون الكفالة لمشروع معين
    }
});

// تشغيل الدوال عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تشغيل تغيير نوع التبرع
    document.getElementById('donation_type').dispatchEvent(new Event('change'));
});
</script>
<?php endif; ?>

<?php include 'includes/footer.php'; ?>
