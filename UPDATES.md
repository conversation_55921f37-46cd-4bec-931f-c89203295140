# تحديثات نظام إدارة التبرعات

## التحديث الجديد - نظام التبرعات المحسن

تم تطوير النظام ليتوافق مع المتطلبات الجديدة لإدارة التبرعات بثلاثة أنواع مختلفة.

---

## 🔄 التغييرات الرئيسية

### 1️⃣ **الكفالات الشهرية**
#### الحقول الجديدة:
- **اسم الكافل** - اسم الشخص الكافل
- **الجنس** - ذكر أو أنثى
- **رقم الهاتف** - للتواصل
- **الولاية** - من 58 ولاية جزائرية
- **القيمة الشهرية** - المبلغ الشهري للكفالة
- **رقم العائلة** - رقم العائلة المكفولة
- **الشهر** - شهر الكفالة (1-12)
- **السنة** - سنة الكفالة
- **رقم تسجيل القيمة** - بلون أخضر فاتح للنقدي، أزرق فاتح للبنكي
- **رقم السحب** - يظهر عند اختيار "عبر الحساب"
- **سند الصرف** - يملأ لاحقاً عند الصرف

### 2️⃣ **التبرعات المقيدة**
#### الحقول الجديدة:
- **المتبرع(ة)** - اسم المتبرع
- **القيمة** - مبلغ التبرع
- **التاريخ** - تاريخ التبرع
- **طريقة الدفع** - نقدي أو عبر الحساب
- **رقم التسجيل** - مع تلوين حسب طريقة الدفع
- **رقم السحب** - للتبرعات البنكية
- **أرقام الصرف** - قائمة من 01 إلى 50000
- **نوع التقييد** - لعائلة أو لمشروع
- **رقم ملف العائلة** - عند التقييد لعائلة
- **نوع المشروع** - اختيار من 15 مشروع

#### المشاريع المتاحة:
1. شراء البيوت
2. ترميم بيوت الأيتام
3. تأثيث بيوت الأيتام
4. تسديد حقوق الكراء
5. تفريج الكربات
6. الحالات الصحية
7. الترفيه
8. جبر خواطر الأيتام
9. قفة رمضان
10. كسوة الأيتام
11. عيد الأضحى
12. الدخول المدرسي
13. اليتيم المتميز
14. الأرملة المنتجة
15. الأرملة المثالية

### 3️⃣ **التبرعات غير المقيدة**
#### الحقول الجديدة:
- **المتبرع(ة)** - اسم المتبرع
- **القيمة** - مبلغ التبرع
- **التاريخ** - تاريخ التبرع
- **طريقة الدفع** - نقدي أو عبر الحساب
- **رقم التسجيل** - مع تلوين حسب طريقة الدفع
- **رقم السحب** - للتبرعات البنكية
- **أرقام الصرف** - قائمة من 01 إلى 50000

---

## 🗄️ تحديثات قاعدة البيانات

### جداول جديدة:
1. **projects** - جدول المشاريع المتاحة
2. **states** - جدول الولايات الجزائرية (58 ولاية)

### تحديث جدول donations:
- إعادة هيكلة كاملة للجدول
- إضافة حقول جديدة لكل نوع تبرع
- تحسين الفهرسة والأداء

---

## 🎨 تحسينات الواجهة

### نموذج إضافة التبرع:
- **تبويبات ديناميكية** - اختيار نوع التبرع بأزرار
- **حقول تفاعلية** - تظهر وتختفي حسب النوع
- **تلوين تلقائي** - للحقول حسب طريقة الدفع
- **قوائم منسدلة** - للولايات والمشاريع
- **تحقق من البيانات** - فحص شامل قبل الحفظ

### قائمة التبرعات:
- **عرض محسن** - معلومات مفصلة لكل نوع
- **بحث متقدم** - بحث في جميع الحقول
- **فلترة ذكية** - حسب النوع والتاريخ
- **تصدير محسن** - Excel مع جميع البيانات

---

## 🚀 خطوات التطبيق

### ✅ **تم الانتهاء من التحديث!**

### 1. تحديث قاعدة البيانات:
```
اذهب إلى: admin/update_database.php
اتبع التعليمات لتحديث الهيكل
```

### 2. استخدام النظام الجديد:
```
اذهب إلى: admin/donations.php
ابدأ في إضافة التبرعات الجديدة
```

### 3. النسخ الاحتياطي:
```
خذ نسخة احتياطية قبل التحديث
استخدم: admin/backup.php
```

### 🗑️ **تم حذف الملفات القديمة:**
- ❌ `admin/donations_new.php` (تم دمجه في donations.php)
- ❌ `setup.php` (لم يعد مطلوب)
- ❌ `simple_setup.php` (لم يعد مطلوب)
- ❌ `test.php` (لم يعد مطلوب)

---

## ⚠️ ملاحظات مهمة

### قبل التحديث:
1. **خذ نسخة احتياطية كاملة**
2. **تأكد من عمل النظام الحالي**
3. **اختبر في بيئة تجريبية أولاً**

### بعد التحديث:
1. **اختبر جميع الوظائف**
2. **تحقق من البيانات المنقولة**
3. **درب المستخدمين على النظام الجديد**

### الملفات الجديدة:
- `admin/donations_new.php` - النظام الجديد
- `admin/update_database.php` - تحديث قاعدة البيانات
- `database/update_donations_structure.sql` - ملف التحديث

---

## 🔧 الدعم الفني

### في حالة وجود مشاكل:
1. تحقق من ملفات السجل
2. راجع إعدادات قاعدة البيانات
3. تأكد من صلاحيات الملفات

### للمساعدة:
- راجع ملف `INSTALL.md`
- تحقق من `logs/error.log`
- استخدم `test.php` للفحص

---

## 📊 الإحصائيات والتقارير

### تقارير جديدة:
- تقارير مفصلة لكل نوع تبرع
- إحصائيات الكفالات الشهرية
- تقارير المشاريع والعائلات
- تحليل طرق الدفع

### تصدير محسن:
- Excel مع جميع التفاصيل
- PDF للإيصالات المحدثة
- تقارير مخصصة حسب الحاجة

---

**تم التطوير بواسطة فريق التطوير - جمعية قوافل الخير**

*آخر تحديث: 2024*
