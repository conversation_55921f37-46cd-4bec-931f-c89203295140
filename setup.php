<?php
/**
 * ملف الإعداد التلقائي لنظام إدارة التبرعات
 * جمعية قوافل الخير
 */

// تعريف ثابت الوصول للنظام في بداية الملف
define('SYSTEM_ACCESS', true);

// التحقق من وجود ملف الإعداد
if (file_exists('includes/config.php')) {
    $config_content = @file_get_contents('includes/config.php');
    if ($config_content && strpos($config_content, 'SYSTEM_INSTALLED') !== false) {
        echo '<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>النظام مثبت بالفعل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>body{font-family:"Cairo",sans-serif;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);min-height:100vh;display:flex;align-items:center;justify-content:center;}</style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h3>النظام مثبت بالفعل</h3>
                        <p>تم تثبيت النظام مسبقاً. يمكنك الوصول إليه من الروابط التالية:</p>
                        <div class="d-grid gap-2">
                            <a href="admin/login.php" class="btn btn-primary">دخول الإدارة</a>
                            <a href="public/index.php" class="btn btn-success">الموقع العام</a>
                        </div>
                        <hr>
                        <small class="text-muted">إذا كنت تريد إعادة التثبيت، احذف ملف includes/config.php</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>';
        exit;
    }
}

$step = $_GET['step'] ?? 1;
$errors = [];
$success = [];

// معالجة خطوات الإعداد
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 1:
            // التحقق من المتطلبات
            $step = 2;
            break;
            
        case 2:
            // إعداد قاعدة البيانات
            $dbHost = $_POST['db_host'] ?? 'localhost';
            $dbName = $_POST['db_name'] ?? 'qawafil_donations';
            $dbUser = $_POST['db_user'] ?? 'root';
            $dbPass = $_POST['db_pass'] ?? '';
            $systemUrl = $_POST['system_url'] ?? 'http://localhost/mosra-kher';
            $resetDatabase = isset($_POST['reset_database']);
            
            try {
                // اختبار الاتصال
                $pdo = new PDO("mysql:host=$dbHost;charset=utf8mb4", $dbUser, $dbPass);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // إنشاء قاعدة البيانات إذا لم تكن موجودة
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                $pdo->exec("USE `$dbName`");

                // فحص وجود الجداول
                $tablesExist = $pdo->query("SHOW TABLES LIKE 'users'")->rowCount() > 0;

                // إعادة تعيين قاعدة البيانات إذا طُلب ذلك
                if ($resetDatabase && $tablesExist) {
                    $pdo->exec("DROP DATABASE `$dbName`");
                    $pdo->exec("CREATE DATABASE `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                    $pdo->exec("USE `$dbName`");
                    $tablesExist = false;
                    $success[] = 'تم حذف قاعدة البيانات القديمة وإنشاء جديدة';
                }

                if (!$tablesExist) {
                    // تشغيل ملف إعداد قاعدة البيانات
                    $sql = file_get_contents('database/setup.sql');
                    $sql = str_replace('qawafil_donations', $dbName, $sql);

                    // تقسيم الاستعلامات وتنفيذها
                    $statements = array_filter(array_map('trim', explode(';', $sql)));
                    foreach ($statements as $statement) {
                        if (!empty($statement)) {
                            $pdo->exec($statement);
                        }
                    }
                    $success[] = 'تم إنشاء قاعدة البيانات والجداول بنجاح';
                } else {
                    // التحقق من وجود البيانات الأساسية
                    $usersCount = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
                    if ($usersCount == 0) {
                        // إدراج البيانات الأساسية فقط
                        $insertSQL = "
                        INSERT INTO users (username, password, full_name, email, role, permissions, status) VALUES
                        ('admin', '\$2y\$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', '<EMAIL>', 'admin', 'all', 'active'),
                        ('employee1', '\$2y\$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'موظف التبرعات', '<EMAIL>', 'employee', 'donations,reports', 'active');

                        INSERT INTO settings (setting_key, setting_value) VALUES
                        ('organization_name', 'جمعية قوافل الخير'),
                        ('next_serial', '10000001'),
                        ('receipt_footer', 'شكراً لكم على تبرعكم الكريم');
                        ";

                        $statements = array_filter(array_map('trim', explode(';', $insertSQL)));
                        foreach ($statements as $statement) {
                            if (!empty($statement)) {
                                $pdo->exec($statement);
                            }
                        }
                        $success[] = 'قاعدة البيانات موجودة - تم إدراج البيانات الأساسية';
                    } else {
                        $success[] = 'قاعدة البيانات موجودة مسبقاً مع البيانات';
                    }
                }
                
                // إنشاء ملف الإعدادات
                $configContent = generateConfigFile($dbHost, $dbName, $dbUser, $dbPass, $systemUrl);

                // التأكد من وجود مجلد includes
                if (!is_dir('includes')) {
                    mkdir('includes', 0755, true);
                }

                if (!file_put_contents('includes/config.php', $configContent)) {
                    throw new Exception('فشل في إنشاء ملف الإعدادات');
                }

                // التحقق من إنشاء الملف بنجاح
                if (!file_exists('includes/config.php')) {
                    throw new Exception('لم يتم إنشاء ملف الإعدادات');
                }

                $success[] = 'تم إعداد قاعدة البيانات بنجاح';
                $step = 3;
                
            } catch (Exception $e) {
                $errors[] = 'خطأ في إعداد قاعدة البيانات: ' . $e->getMessage();
            }
            break;
            
        case 3:
            // إعداد المدير الرئيسي
            $adminUser = $_POST['admin_user'] ?? 'admin';
            $adminPass = $_POST['admin_pass'] ?? '';
            $adminName = $_POST['admin_name'] ?? 'مدير النظام';
            $adminEmail = $_POST['admin_email'] ?? '';
            
            if (empty($adminPass)) {
                $errors[] = 'كلمة مرور المدير مطلوبة';
            } else {
                try {
                    // التحقق من وجود ملف الإعدادات
                    if (!file_exists('includes/config.php')) {
                        throw new Exception('ملف الإعدادات غير موجود. يرجى العودة للخطوة السابقة.');
                    }

                    require_once 'includes/config.php';
                    require_once 'includes/database.php';
                    require_once 'includes/functions.php';

                    // تحديث بيانات المدير
                    $hashedPassword = password_hash($adminPass, PASSWORD_DEFAULT);
                    $updated = $db->update('users', [
                        'username' => $adminUser,
                        'password' => $hashedPassword,
                        'full_name' => $adminName,
                        'email' => $adminEmail
                    ], 'id = 1');

                    if ($updated) {
                        $success[] = 'تم إعداد المدير الرئيسي بنجاح';
                        $step = 4;
                    } else {
                        throw new Exception('فشل في تحديث بيانات المدير');
                    }

                } catch (Exception $e) {
                    $errors[] = 'خطأ في إعداد المدير: ' . $e->getMessage();
                }
            }
            break;
            
        case 4:
            // إعداد معلومات الجمعية
            $orgName = $_POST['org_name'] ?? 'جمعية قوافل الخير';
            $orgAddress = $_POST['org_address'] ?? '';
            $orgPhone = $_POST['org_phone'] ?? '';
            $orgEmail = $_POST['org_email'] ?? '';
            $bankAccount1 = $_POST['bank_account_1'] ?? '';
            $bankAccount2 = $_POST['bank_account_2'] ?? '';
            
            try {
                // التحقق من وجود ملف الإعدادات
                if (!file_exists('includes/config.php')) {
                    throw new Exception('ملف الإعدادات غير موجود. يرجى العودة للخطوة الأولى.');
                }

                require_once 'includes/config.php';
                require_once 'includes/database.php';
                require_once 'includes/functions.php';

                // تحديث إعدادات الجمعية
                $settings = [
                    'organization_name' => $orgName,
                    'organization_address' => $orgAddress,
                    'organization_phone' => $orgPhone,
                    'organization_email' => $orgEmail,
                    'bank_account_1' => $bankAccount1,
                    'bank_account_2' => $bankAccount2
                ];

                foreach ($settings as $key => $value) {
                    $db->update('settings', ['setting_value' => $value], 'setting_key = :key', ['key' => $key]);
                }

                // إضافة علامة التثبيت المكتمل
                $configPath = 'includes/config.php';
                $configContent = file_get_contents($configPath);
                if (strpos($configContent, 'SYSTEM_INSTALLED') === false) {
                    $configContent .= "\n// علامة التثبيت المكتمل\ndefine('SYSTEM_INSTALLED', true);\n";
                    if (!file_put_contents($configPath, $configContent)) {
                        throw new Exception('فشل في تحديث ملف الإعدادات');
                    }
                }

                $success[] = 'تم إعداد النظام بنجاح!';
                $step = 5;

            } catch (Exception $e) {
                $errors[] = 'خطأ في إعداد معلومات الجمعية: ' . $e->getMessage();
            }
            break;
    }
}

// دالة إنشاء ملف الإعدادات
function generateConfigFile($dbHost, $dbName, $dbUser, $dbPass, $systemUrl) {
    return '<?php
/**
 * ملف الإعدادات الأساسي لنظام إدارة التبرعات
 * جمعية قوافل الخير
 * تم إنشاؤه تلقائياً في: ' . date('Y-m-d H:i:s') . '
 */

// منع الوصول المباشر
if (!defined(\'SYSTEM_ACCESS\')) {
    die(\'Access denied\');
}

// إعدادات قاعدة البيانات
define(\'DB_HOST\', \'' . addslashes($dbHost) . '\');
define(\'DB_NAME\', \'' . addslashes($dbName) . '\');
define(\'DB_USER\', \'' . addslashes($dbUser) . '\');
define(\'DB_PASS\', \'' . addslashes($dbPass) . '\');
define(\'DB_CHARSET\', \'utf8mb4\');

// إعدادات النظام
define(\'SYSTEM_NAME\', \'نظام إدارة التبرعات - جمعية قوافل الخير\');
define(\'SYSTEM_VERSION\', \'1.0.0\');
define(\'SYSTEM_URL\', \'' . addslashes($systemUrl) . '\');
define(\'ADMIN_URL\', SYSTEM_URL . \'/admin\');
define(\'PUBLIC_URL\', SYSTEM_URL . \'/public\');

// مسارات الملفات
define(\'ROOT_PATH\', dirname(__DIR__));
define(\'INCLUDES_PATH\', ROOT_PATH . \'/includes\');
define(\'UPLOADS_PATH\', ROOT_PATH . \'/uploads\');
define(\'RECEIPTS_PATH\', UPLOADS_PATH . \'/receipts\');
define(\'BACKUPS_PATH\', UPLOADS_PATH . \'/backups\');

// إعدادات الجلسة
define(\'SESSION_NAME\', \'qawafil_session\');
define(\'SESSION_LIFETIME\', 3600);

// إعدادات الأمان
define(\'HASH_ALGO\', \'sha256\');
define(\'ENCRYPTION_KEY\', \'qawafil_donations_2024_secret_key\');

// إعدادات التحميل
define(\'MAX_FILE_SIZE\', 5 * 1024 * 1024);
define(\'ALLOWED_EXTENSIONS\', [\'pdf\', \'jpg\', \'jpeg\', \'png\']);

// إعدادات PDF
define(\'PDF_FONT\', \'DejaVuSans\');
define(\'PDF_FONT_SIZE\', 12);

// إعدادات التاريخ والوقت
date_default_timezone_set(\'Asia/Riyadh\');
define(\'DATE_FORMAT\', \'Y-m-d\');
define(\'DATETIME_FORMAT\', \'Y-m-d H:i:s\');
define(\'DISPLAY_DATE_FORMAT\', \'d/m/Y\');
define(\'DISPLAY_DATETIME_FORMAT\', \'d/m/Y H:i\');

// إعدادات اللغة
define(\'SYSTEM_LANG\', \'ar\');
define(\'SYSTEM_DIRECTION\', \'rtl\');

// إعدادات التصحيح
define(\'DEBUG_MODE\', false);
define(\'LOG_ERRORS\', true);
define(\'ERROR_LOG_FILE\', ROOT_PATH . \'/logs/error.log\');

// أنواع التبرعات
define(\'DONATION_TYPES\', [
    \'monthly_sponsorship\' => \'كفالة شهرية\',
    \'restricted\' => \'تبرع مقيد\',
    \'unrestricted\' => \'تبرع غير مقيد\'
]);

// طرق الدفع
define(\'PAYMENT_METHODS\', [
    \'cash\' => \'نقدي\',
    \'bank_transfer\' => \'تحويل بنكي\',
    \'card\' => \'بطاقة ائتمانية\',
    \'online\' => \'دفع إلكتروني\'
]);

// مستويات الصلاحيات
define(\'USER_ROLES\', [
    \'admin\' => \'مدير النظام\',
    \'employee\' => \'موظف\',
    \'user\' => \'مستخدم\'
]);

// الصلاحيات المتاحة
define(\'PERMISSIONS\', [
    \'users\' => \'إدارة المستخدمين\',
    \'donations\' => \'إدارة التبرعات\',
    \'reports\' => \'التقارير والإحصائيات\',
    \'backup\' => \'النسخ الاحتياطي\',
    \'settings\' => \'إعدادات النظام\'
]);

// إنشاء المجلدات المطلوبة إذا لم تكن موجودة
$required_dirs = [
    UPLOADS_PATH,
    RECEIPTS_PATH,
    BACKUPS_PATH,
    dirname(ERROR_LOG_FILE)
];

foreach ($required_dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}
?>';
}

// دالة التحقق من المتطلبات
function checkRequirements() {
    $requirements = [
        'PHP Version >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
        'PDO Extension' => extension_loaded('pdo'),
        'PDO MySQL Extension' => extension_loaded('pdo_mysql'),
        'GD Extension' => extension_loaded('gd'),
        'mbstring Extension' => extension_loaded('mbstring'),
        'JSON Extension' => extension_loaded('json'),
        'cURL Extension' => extension_loaded('curl'),
        'uploads/ Directory Writable' => is_writable('uploads') || mkdir('uploads', 0755, true),
        'logs/ Directory Writable' => is_writable('logs') || mkdir('logs', 0755, true)
    ];
    
    return $requirements;
}

$requirements = checkRequirements();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد نظام إدارة التبرعات - جمعية قوافل الخير</title>
    
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .setup-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .setup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .setup-body {
            padding: 2rem;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
        }
        
        .step.active {
            background: #667eea;
            color: white;
        }
        
        .step.completed {
            background: #28a745;
            color: white;
        }
        
        .requirement-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .requirement-status {
            font-weight: bold;
        }
        
        .status-ok {
            color: #28a745;
        }
        
        .status-error {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1>إعداد نظام إدارة التبرعات</h1>
            <p>جمعية قوافل الخير</p>
        </div>
        
        <div class="setup-body">
            <!-- مؤشر الخطوات -->
            <div class="step-indicator">
                <div class="step <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : ''; ?>">1</div>
                <div class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : ''; ?>">2</div>
                <div class="step <?php echo $step >= 3 ? ($step > 3 ? 'completed' : 'active') : ''; ?>">3</div>
                <div class="step <?php echo $step >= 4 ? ($step > 4 ? 'completed' : 'active') : ''; ?>">4</div>
                <div class="step <?php echo $step >= 5 ? 'completed' : ''; ?>">5</div>
            </div>
            
            <!-- عرض الرسائل -->
            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <?php foreach ($errors as $error): ?>
                        <div><i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?></div>
                    <?php endforeach; ?>
                    <?php if ($step > 1): ?>
                        <hr>
                        <a href="setup.php?step=<?php echo $step - 1; ?>" class="btn btn-sm btn-outline-danger">
                            <i class="fas fa-arrow-right me-1"></i>
                            العودة للخطوة السابقة
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($success)): ?>
                <div class="alert alert-success">
                    <?php foreach ($success as $msg): ?>
                        <div><i class="fas fa-check-circle me-2"></i><?php echo $msg; ?></div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($step == 1): ?>
                <!-- الخطوة 1: التحقق من المتطلبات -->
                <h3>الخطوة 1: التحقق من المتطلبات</h3>
                
                <div class="requirements-list">
                    <?php foreach ($requirements as $req => $status): ?>
                        <div class="requirement-item">
                            <span><?php echo $req; ?></span>
                            <span class="requirement-status <?php echo $status ? 'status-ok' : 'status-error'; ?>">
                                <?php if ($status): ?>
                                    <i class="fas fa-check"></i> متوفر
                                <?php else: ?>
                                    <i class="fas fa-times"></i> غير متوفر
                                <?php endif; ?>
                            </span>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <?php if (array_product($requirements)): ?>
                    <form method="POST" action="?step=1">
                        <button type="submit" class="btn btn-primary mt-3">
                            <i class="fas fa-arrow-left me-2"></i>
                            متابعة إلى الخطوة التالية
                        </button>
                    </form>
                <?php else: ?>
                    <div class="alert alert-warning mt-3">
                        يرجى تثبيت المتطلبات المفقودة قبل المتابعة
                    </div>
                <?php endif; ?>
                
            <?php elseif ($step == 2): ?>
                <!-- الخطوة 2: إعداد قاعدة البيانات -->
                <h3>الخطوة 2: إعداد قاعدة البيانات</h3>
                
                <form method="POST" action="?step=2">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="db_host" class="form-label">خادم قاعدة البيانات</label>
                                <input type="text" class="form-control" id="db_host" name="db_host" 
                                       value="localhost" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="db_name" class="form-label">اسم قاعدة البيانات</label>
                                <input type="text" class="form-control" id="db_name" name="db_name" 
                                       value="qawafil_donations" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="db_user" class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" id="db_user" name="db_user" 
                                       value="root" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="db_pass" class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" id="db_pass" name="db_pass">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="system_url" class="form-label">رابط النظام</label>
                        <input type="url" class="form-control" id="system_url" name="system_url"
                               value="http://localhost/mosra-kher" required>
                        <div class="form-text">الرابط الكامل للنظام بدون / في النهاية</div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="reset_database" name="reset_database">
                            <label class="form-check-label text-danger" for="reset_database">
                                <strong>حذف قاعدة البيانات الموجودة وإعادة إنشائها</strong>
                            </label>
                            <div class="form-text text-danger">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                تحذير: سيتم حذف جميع البيانات الموجودة!
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-database me-2"></i>
                        إعداد قاعدة البيانات
                    </button>
                </form>
                
            <?php elseif ($step == 3): ?>
                <!-- الخطوة 3: إعداد المدير الرئيسي -->
                <h3>الخطوة 3: إعداد المدير الرئيسي</h3>
                
                <form method="POST" action="?step=3">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="admin_user" class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" id="admin_user" name="admin_user" 
                                       value="admin" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="admin_pass" class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" id="admin_pass" name="admin_pass" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="admin_name" class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" id="admin_name" name="admin_name" 
                                       value="مدير النظام" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="admin_email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="admin_email" name="admin_email">
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-user-shield me-2"></i>
                        إعداد المدير
                    </button>
                </form>
                
            <?php elseif ($step == 4): ?>
                <!-- الخطوة 4: إعداد معلومات الجمعية -->
                <h3>الخطوة 4: إعداد معلومات الجمعية</h3>
                
                <form method="POST" action="?step=4">
                    <div class="mb-3">
                        <label for="org_name" class="form-label">اسم الجمعية</label>
                        <input type="text" class="form-control" id="org_name" name="org_name" 
                               value="جمعية قوافل الخير" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="org_address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="org_address" name="org_address" rows="2"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="org_phone" class="form-label">رقم الهاتف</label>
                                <input type="text" class="form-control" id="org_phone" name="org_phone">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="org_email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="org_email" name="org_email">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="bank_account_1" class="form-label">الحساب البنكي الأول</label>
                                <input type="text" class="form-control" id="bank_account_1" name="bank_account_1">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="bank_account_2" class="form-label">الحساب البنكي الثاني</label>
                                <input type="text" class="form-control" id="bank_account_2" name="bank_account_2">
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check me-2"></i>
                        إنهاء الإعداد
                    </button>
                </form>
                
            <?php elseif ($step == 5): ?>
                <!-- الخطوة 5: اكتمال الإعداد -->
                <div class="text-center">
                    <i class="fas fa-check-circle fa-5x text-success mb-4"></i>
                    <h3 class="text-success">تم إعداد النظام بنجاح!</h3>
                    <p class="mb-4">يمكنك الآن البدء في استخدام نظام إدارة التبرعات</p>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5>لوحة التحكم</h5>
                                    <p>دخول الإدارة وإدارة النظام</p>
                                    <a href="admin/login.php" class="btn btn-primary">
                                        <i class="fas fa-sign-in-alt me-2"></i>
                                        دخول الإدارة
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5>الموقع العام</h5>
                                    <p>عرض الموقع العام للجمعية</p>
                                    <a href="public/index.php" class="btn btn-success">
                                        <i class="fas fa-globe me-2"></i>
                                        الموقع العام
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-4">
                        <strong>ملاحظة مهمة:</strong> احذف ملف setup.php من الخادم لأسباب أمنية
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
