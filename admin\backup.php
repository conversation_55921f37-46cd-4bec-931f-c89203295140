<?php
/**
 * صفحة النسخ الاحتياطي
 * جمعية قوافل الخير
 */

define('SYSTEM_ACCESS', true);
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// التحقق من الصلاحية
requirePermission('backup');

$action = $_GET['action'] ?? 'list';

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'create') {
        $filename = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
        $backupFile = $db->backup($filename);
        
        if ($backupFile) {
            logActivity('create_backup', null, null, null, ['filename' => $backupFile]);
            redirect('backup.php', 'تم إنشاء النسخة الاحتياطية بنجاح: ' . $backupFile, 'success');
        } else {
            setFlashMessage('فشل في إنشاء النسخة الاحتياطية', 'error');
        }
    } elseif ($action === 'restore') {
        $filename = sanitize($_POST['backup_file']);
        
        if ($db->restore($filename)) {
            logActivity('restore_backup', null, null, null, ['filename' => $filename]);
            redirect('backup.php', 'تم استعادة النسخة الاحتياطية بنجاح', 'success');
        } else {
            setFlashMessage('فشل في استعادة النسخة الاحتياطية', 'error');
        }
    } elseif ($action === 'upload') {
        if (isset($_FILES['backup_file']) && $_FILES['backup_file']['error'] === UPLOAD_ERR_OK) {
            $uploadResult = uploadFile($_FILES['backup_file'], BACKUPS_PATH, null);
            
            if ($uploadResult['success']) {
                logActivity('upload_backup', null, null, null, ['filename' => $uploadResult['filename']]);
                redirect('backup.php', 'تم رفع ملف النسخة الاحتياطية بنجاح', 'success');
            } else {
                setFlashMessage($uploadResult['message'], 'error');
            }
        } else {
            setFlashMessage('يرجى اختيار ملف صحيح', 'error');
        }
    }
}

// حذف النسخة الاحتياطية
if ($action === 'delete') {
    $filename = sanitize($_GET['file'] ?? '');
    $filePath = BACKUPS_PATH . '/' . $filename;
    
    if (file_exists($filePath) && deleteFile($filePath)) {
        logActivity('delete_backup', null, null, null, ['filename' => $filename]);
        redirect('backup.php', 'تم حذف النسخة الاحتياطية بنجاح', 'success');
    } else {
        redirect('backup.php', 'فشل في حذف النسخة الاحتياطية', 'error');
    }
}

// تحميل النسخة الاحتياطية
if ($action === 'download') {
    $filename = sanitize($_GET['file'] ?? '');
    $filePath = BACKUPS_PATH . '/' . $filename;
    
    if (file_exists($filePath)) {
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . filesize($filePath));
        readfile($filePath);
        exit;
    } else {
        redirect('backup.php', 'الملف غير موجود', 'error');
    }
}

// قائمة النسخ الاحتياطية
$backupFiles = [];
if (is_dir(BACKUPS_PATH)) {
    $files = scandir(BACKUPS_PATH);
    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..' && pathinfo($file, PATHINFO_EXTENSION) === 'sql') {
            $filePath = BACKUPS_PATH . '/' . $file;
            $backupFiles[] = [
                'name' => $file,
                'size' => filesize($filePath),
                'date' => filemtime($filePath)
            ];
        }
    }
    
    // ترتيب حسب التاريخ (الأحدث أولاً)
    usort($backupFiles, function($a, $b) {
        return $b['date'] - $a['date'];
    });
}

// معلومات قاعدة البيانات
$dbSize = $db->getDatabaseSize();
$tablesInfo = $db->select("
    SELECT 
        table_name,
        table_rows,
        round(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
    FROM information_schema.TABLES 
    WHERE table_schema = :database
    ORDER BY size_mb DESC
", ['database' => DB_NAME]);

$pageTitle = 'النسخ الاحتياطي';
include 'includes/header.php';
?>

<!-- معلومات قاعدة البيانات -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            حجم قاعدة البيانات
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo number_format($dbSize, 2); ?> ميجابايت
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-database fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            عدد الجداول
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo count($tablesInfo); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-table fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            النسخ الاحتياطية
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo count($backupFiles); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-archive fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            آخر نسخة احتياطية
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php 
                            if (!empty($backupFiles)) {
                                echo formatDate($backupFiles[0]['date']);
                            } else {
                                echo 'لا توجد';
                            }
                            ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- إنشاء نسخة احتياطية -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">إنشاء نسخة احتياطية جديدة</h6>
            </div>
            <div class="card-body">
                <p class="text-muted">
                    إنشاء نسخة احتياطية كاملة من قاعدة البيانات تتضمن جميع الجداول والبيانات.
                </p>
                
                <form method="POST" action="?action=create">
                    <button type="submit" class="btn btn-primary" 
                            onclick="return confirm('هل أنت متأكد من إنشاء نسخة احتياطية جديدة؟')">
                        <i class="fas fa-plus"></i> إنشاء نسخة احتياطية
                    </button>
                </form>
                
                <hr>
                
                <h6 class="font-weight-bold">رفع نسخة احتياطية</h6>
                <form method="POST" action="?action=upload" enctype="multipart/form-data">
                    <div class="form-group mb-3">
                        <input type="file" class="form-control" name="backup_file" 
                               accept=".sql" required>
                        <small class="form-text text-muted">
                            يجب أن يكون الملف بصيغة .sql
                        </small>
                    </div>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-upload"></i> رفع الملف
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- معلومات الجداول -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">معلومات الجداول</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive" style="max-height: 300px;">
                    <table class="table table-bordered table-sm">
                        <thead>
                            <tr>
                                <th>اسم الجدول</th>
                                <th>عدد السجلات</th>
                                <th>الحجم (ميجابايت)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($tablesInfo as $table): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($table['table_name']); ?></td>
                                    <td><?php echo number_format($table['table_rows']); ?></td>
                                    <td><?php echo number_format($table['size_mb'], 2); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قائمة النسخ الاحتياطية -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">النسخ الاحتياطية المتاحة</h6>
    </div>
    <div class="card-body">
        <?php if (!empty($backupFiles)): ?>
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>اسم الملف</th>
                            <th>الحجم</th>
                            <th>تاريخ الإنشاء</th>
                            <th>العمليات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($backupFiles as $file): ?>
                            <tr>
                                <td>
                                    <i class="fas fa-file-archive text-primary me-2"></i>
                                    <?php echo htmlspecialchars($file['name']); ?>
                                </td>
                                <td><?php echo number_format($file['size'] / 1024 / 1024, 2); ?> ميجابايت</td>
                                <td><?php echo formatDateTime($file['date']); ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="backup.php?action=download&file=<?php echo urlencode($file['name']); ?>" 
                                           class="btn btn-sm btn-primary" title="تحميل">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-success" 
                                                onclick="restoreBackup('<?php echo htmlspecialchars($file['name']); ?>')"
                                                title="استعادة">
                                            <i class="fas fa-undo"></i>
                                        </button>
                                        <a href="backup.php?action=delete&file=<?php echo urlencode($file['name']); ?>" 
                                           class="btn btn-sm btn-danger" title="حذف"
                                           onclick="return confirmDelete('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-4">
                <i class="fas fa-archive fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد نسخ احتياطية</h5>
                <p class="text-muted">لم يتم إنشاء أي نسخ احتياطية بعد</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal لاستعادة النسخة الاحتياطية -->
<div class="modal fade" id="restoreModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">استعادة النسخة الاحتياطية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> ستؤدي هذه العملية إلى استبدال جميع البيانات الحالية بالبيانات الموجودة في النسخة الاحتياطية.
                    هذه العملية لا يمكن التراجع عنها!
                </div>
                
                <p>هل أنت متأكد من استعادة النسخة الاحتياطية: <strong id="backupFileName"></strong>؟</p>
                
                <form method="POST" action="?action=restore" id="restoreForm">
                    <input type="hidden" name="backup_file" id="backupFileInput">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" onclick="confirmRestore()">
                    <i class="fas fa-undo"></i> استعادة النسخة الاحتياطية
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function restoreBackup(filename) {
    document.getElementById('backupFileName').textContent = filename;
    document.getElementById('backupFileInput').value = filename;
    
    const modal = new bootstrap.Modal(document.getElementById('restoreModal'));
    modal.show();
}

function confirmRestore() {
    document.getElementById('restoreForm').submit();
}

// تحديث الصفحة كل 30 ثانية لعرض آخر النسخ الاحتياطية
setInterval(function() {
    if (!document.querySelector('.modal.show')) {
        location.reload();
    }
}, 30000);
</script>

<?php include 'includes/footer.php'; ?>
