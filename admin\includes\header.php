<?php
/**
 * هيدر لوحة التحكم
 * جمعية قوافل الخير
 */

// منع الوصول المباشر
if (!defined('SYSTEM_ACCESS')) {
    die('Access denied');
}

// الحصول على الرسائل المؤقتة
$flashMessages = getFlashMessages();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' : ''; ?><?php echo SYSTEM_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fc;
        }
        
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 0 35px 0 rgba(154, 161, 171, 0.15);
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 1rem 1.5rem;
            border-radius: 0.35rem;
            margin: 0.25rem 1rem;
            transition: all 0.3s;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .sidebar .nav-link i {
            margin-left: 0.5rem;
            width: 1.5rem;
            text-align: center;
        }
        
        .navbar-brand {
            font-weight: 700;
            color: white !important;
        }
        
        .topbar {
            background: white;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }
        
        .card {
            border: none;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }
        
        .border-right-primary {
            border-left: 0.25rem solid #4e73df !important;
        }
        
        .border-right-success {
            border-left: 0.25rem solid #1cc88a !important;
        }
        
        .border-right-info {
            border-left: 0.25rem solid #36b9cc !important;
        }
        
        .border-right-warning {
            border-left: 0.25rem solid #f6c23e !important;
        }
        
        .text-xs {
            font-size: 0.7rem;
        }
        
        .btn-circle {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .dropdown-list {
            max-height: 15rem;
            overflow-y: auto;
        }
        
        .progress {
            height: 0.5rem;
        }
        
        .badge-pill {
            border-radius: 10rem;
        }
        
        .list-group-item {
            border: none;
            border-bottom: 1px solid #e3e6f0;
        }
        
        .list-group-item:last-child {
            border-bottom: none;
        }
        
        .main-content {
            margin-right: 250px;
        }
        
        @media (max-width: 768px) {
            .main-content {
                margin-right: 0;
            }
            
            .sidebar {
                position: fixed;
                top: 0;
                right: -250px;
                width: 250px;
                height: 100vh;
                z-index: 1050;
                transition: right 0.3s ease;
            }
            
            .sidebar.show {
                right: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar position-fixed top-0 end-0" style="width: 250px;">
        <div class="p-3">
            <a class="navbar-brand d-flex align-items-center justify-content-center" href="dashboard.php">
                <div class="sidebar-brand-icon">
                    <i class="fas fa-heart"></i>
                </div>
                <div class="sidebar-brand-text mx-2">قوافل الخير</div>
            </a>
        </div>
        
        <hr class="sidebar-divider my-0" style="border-color: rgba(255, 255, 255, 0.15);">
        
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>" 
                   href="dashboard.php">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </a>
            </li>
            
            <?php if ($auth->hasPermission('donations')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'donations.php' ? 'active' : ''; ?>" 
                   href="donations.php">
                    <i class="fas fa-fw fa-donate"></i>
                    <span>إدارة التبرعات</span>
                </a>
            </li>
            <?php endif; ?>
            
            <?php if ($auth->hasPermission('users')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'users.php' ? 'active' : ''; ?>" 
                   href="users.php">
                    <i class="fas fa-fw fa-users"></i>
                    <span>إدارة المستخدمين</span>
                </a>
            </li>
            <?php endif; ?>
            
            <?php if ($auth->hasPermission('reports')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'reports.php' ? 'active' : ''; ?>" 
                   href="reports.php">
                    <i class="fas fa-fw fa-chart-area"></i>
                    <span>التقارير والإحصائيات</span>
                </a>
            </li>
            <?php endif; ?>
            
            <?php if ($auth->hasPermission('backup')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'backup.php' ? 'active' : ''; ?>" 
                   href="backup.php">
                    <i class="fas fa-fw fa-database"></i>
                    <span>النسخ الاحتياطي</span>
                </a>
            </li>
            <?php endif; ?>
            
            <hr class="sidebar-divider" style="border-color: rgba(255, 255, 255, 0.15);">
            
            <li class="nav-item">
                <a class="nav-link" href="../public/index.php" target="_blank">
                    <i class="fas fa-fw fa-globe"></i>
                    <span>الموقع العام</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="logout.php">
                    <i class="fas fa-fw fa-sign-out-alt"></i>
                    <span>تسجيل الخروج</span>
                </a>
            </li>
        </ul>
    </nav>
    
    <!-- Main Content -->
    <div class="main-content">
        <!-- Topbar -->
        <nav class="navbar navbar-expand navbar-light topbar mb-4 static-top">
            <!-- Sidebar Toggle (Mobile) -->
            <button class="btn btn-link d-md-none rounded-circle me-3" id="sidebarToggle">
                <i class="fa fa-bars"></i>
            </button>
            
            <!-- Topbar Navbar -->
            <ul class="navbar-nav ms-auto">
                <!-- User Information -->
                <li class="nav-item dropdown no-arrow">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                       data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <span class="me-2 d-none d-lg-inline text-gray-600 small">
                            <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                        </span>
                        <i class="fas fa-user-circle fa-lg"></i>
                    </a>
                    <!-- Dropdown - User Information -->
                    <div class="dropdown-menu dropdown-menu-end shadow animated--grow-in"
                         aria-labelledby="userDropdown">
                        <a class="dropdown-item" href="profile.php">
                            <i class="fas fa-user fa-sm fa-fw me-2 text-gray-400"></i>
                            الملف الشخصي
                        </a>
                        <a class="dropdown-item" href="settings.php">
                            <i class="fas fa-cogs fa-sm fa-fw me-2 text-gray-400"></i>
                            الإعدادات
                        </a>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="logout.php">
                            <i class="fas fa-sign-out-alt fa-sm fa-fw me-2 text-gray-400"></i>
                            تسجيل الخروج
                        </a>
                    </div>
                </li>
            </ul>
        </nav>
        
        <!-- Page Content -->
        <div class="container-fluid">
            <!-- Flash Messages -->
            <?php if (!empty($flashMessages)): ?>
                <?php foreach ($flashMessages as $message): ?>
                    <div class="alert alert-<?php echo $message['type'] === 'error' ? 'danger' : $message['type']; ?> alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($message['message']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
            
            <!-- Page Heading -->
            <?php if (isset($pageTitle)): ?>
                <div class="d-sm-flex align-items-center justify-content-between mb-4">
                    <h1 class="h3 mb-0 text-gray-800"><?php echo $pageTitle; ?></h1>
                </div>
            <?php endif; ?>
