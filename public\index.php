<?php
/**
 * الصفحة الرئيسية العامة
 * جمعية قوافل الخير
 */

define('SYSTEM_ACCESS', true);
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';

// الحصول على إعدادات الجمعية
$orgName = getSetting('organization_name', 'جمعية قوافل الخير');
$orgAddress = getSetting('organization_address', '');
$orgPhone = getSetting('organization_phone', '');
$orgEmail = getSetting('organization_email', '');
$bankAccount1 = getSetting('bank_account_1', '');
$bankAccount2 = getSetting('bank_account_2', '');

// الحصول على إحصائيات عامة
$totalDonations = $db->selectOne("SELECT COUNT(*) as count FROM donations")['count'] ?? 0;
$totalAmount = $db->selectOne("SELECT SUM(amount) as total FROM donations")['total'] ?? 0;
$totalDonors = $db->selectOne("SELECT COUNT(*) as count FROM donors")['count'] ?? 0;

// آخر التبرعات (بدون أسماء للخصوصية)
$recentDonations = $db->select("
    SELECT donation_type, amount, currency, created_at
    FROM donations 
    ORDER BY created_at DESC 
    LIMIT 5
");
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($orgName); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0;
            text-align: center;
        }
        
        .hero-section h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .hero-section p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .btn-donate {
            background: #28a745;
            border: none;
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            transition: all 0.3s ease;
        }
        
        .btn-donate:hover {
            background: #218838;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3);
        }
        
        .stats-section {
            padding: 80px 0;
            background: #f8f9fa;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            margin-bottom: 2rem;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 1.1rem;
            color: #666;
            font-weight: 600;
        }
        
        .stat-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .donation-types {
            padding: 80px 0;
        }
        
        .donation-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            margin-bottom: 2rem;
            height: 100%;
        }
        
        .donation-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .donation-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .icon-sponsorship {
            color: #4e73df;
        }
        
        .icon-restricted {
            color: #f6c23e;
        }
        
        .icon-unrestricted {
            color: #1cc88a;
        }
        
        .bank-info {
            padding: 80px 0;
            background: #f8f9fa;
        }
        
        .bank-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        .bank-card h5 {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .account-number {
            font-size: 1.2rem;
            font-weight: 700;
            color: #333;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .recent-donations {
            padding: 80px 0;
        }
        
        .donation-item {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-right: 4px solid #667eea;
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            padding: 50px 0 30px;
        }
        
        .footer h5 {
            color: #ecf0f1;
            margin-bottom: 1rem;
        }
        
        .footer p, .footer a {
            color: #bdc3c7;
        }
        
        .footer a:hover {
            color: white;
        }
        
        .contact-info {
            margin-bottom: 1rem;
        }
        
        .contact-info i {
            width: 20px;
            margin-left: 10px;
        }
        
        @media (max-width: 768px) {
            .hero-section h1 {
                font-size: 2.5rem;
            }
            
            .stat-number {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: rgba(44, 62, 80, 0.95);">
        <div class="container">
            <a class="navbar-brand fw-bold" href="#">
                <i class="fas fa-heart me-2"></i>
                <?php echo htmlspecialchars($orgName); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#donations">أنواع التبرعات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#bank-info">الحسابات البنكية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">اتصل بنا</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../admin/login.php">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            دخول الإدارة
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="container">
            <h1><?php echo htmlspecialchars($orgName); ?></h1>
            <p>نعمل معاً لنشر الخير ومساعدة المحتاجين</p>
            <a href="#donations" class="btn btn-success btn-donate">
                <i class="fas fa-heart me-2"></i>
                تبرع الآن
            </a>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="stats-section">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="fw-bold mb-3">إنجازاتنا بالأرقام</h2>
                    <p class="text-muted">نفخر بما حققناه بفضل تبرعاتكم الكريمة</p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-lg-4 col-md-6">
                    <div class="stat-card">
                        <i class="fas fa-donate stat-icon"></i>
                        <div class="stat-number"><?php echo number_format($totalDonations); ?></div>
                        <div class="stat-label">إجمالي التبرعات</div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="stat-card">
                        <i class="fas fa-dollar-sign stat-icon"></i>
                        <div class="stat-number"><?php echo number_format($totalAmount); ?></div>
                        <div class="stat-label">إجمالي المبلغ (ريال)</div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="stat-card">
                        <i class="fas fa-users stat-icon"></i>
                        <div class="stat-number"><?php echo number_format($totalDonors); ?></div>
                        <div class="stat-label">عدد المتبرعين</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Donation Types Section -->
    <section id="donations" class="donation-types">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="fw-bold mb-3">أنواع التبرعات</h2>
                    <p class="text-muted">اختر نوع التبرع الذي يناسبك</p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-lg-4 col-md-6">
                    <div class="donation-card">
                        <i class="fas fa-hand-holding-heart donation-icon icon-sponsorship"></i>
                        <h4 class="fw-bold mb-3">الكفالات الشهرية</h4>
                        <p class="text-muted">
                            كفالة الأيتام والأسر المحتاجة بمبلغ شهري ثابت لضمان استمرارية الدعم
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="donation-card">
                        <i class="fas fa-project-diagram donation-icon icon-restricted"></i>
                        <h4 class="fw-bold mb-3">التبرعات المقيدة</h4>
                        <p class="text-muted">
                            تبرعات مخصصة لمشاريع أو عوائل محددة حسب رغبة المتبرع
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="donation-card">
                        <i class="fas fa-hands-helping donation-icon icon-unrestricted"></i>
                        <h4 class="fw-bold mb-3">التبرعات العامة</h4>
                        <p class="text-muted">
                            تبرعات عامة توجه لأكثر الحالات احتياجاً حسب تقدير الجمعية
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bank Information Section -->
    <section id="bank-info" class="bank-info">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="fw-bold mb-3">الحسابات البنكية</h2>
                    <p class="text-muted">يمكنكم التبرع عبر الحسابات البنكية التالية</p>
                </div>
            </div>
            
            <div class="row justify-content-center">
                <?php if ($bankAccount1): ?>
                <div class="col-lg-5 col-md-6">
                    <div class="bank-card text-center">
                        <i class="fas fa-university fa-3x text-primary mb-3"></i>
                        <h5>الحساب البنكي الأول</h5>
                        <div class="account-number"><?php echo htmlspecialchars($bankAccount1); ?></div>
                        <button class="btn btn-outline-primary btn-sm" 
                                onclick="copyToClipboard('<?php echo htmlspecialchars($bankAccount1); ?>')">
                            <i class="fas fa-copy me-1"></i>
                            نسخ رقم الحساب
                        </button>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if ($bankAccount2): ?>
                <div class="col-lg-5 col-md-6">
                    <div class="bank-card text-center">
                        <i class="fas fa-university fa-3x text-success mb-3"></i>
                        <h5>الحساب البنكي الثاني</h5>
                        <div class="account-number"><?php echo htmlspecialchars($bankAccount2); ?></div>
                        <button class="btn btn-outline-success btn-sm" 
                                onclick="copyToClipboard('<?php echo htmlspecialchars($bankAccount2); ?>')">
                            <i class="fas fa-copy me-1"></i>
                            نسخ رقم الحساب
                        </button>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Recent Donations Section -->
    <?php if (!empty($recentDonations)): ?>
    <section class="recent-donations">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="fw-bold mb-3">آخر التبرعات</h2>
                    <p class="text-muted">شكراً لجميع المتبرعين الكرام</p>
                </div>
            </div>
            
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <?php foreach ($recentDonations as $donation): ?>
                        <div class="donation-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1"><?php echo DONATION_TYPES[$donation['donation_type']]; ?></h6>
                                    <small class="text-muted"><?php echo formatDateTime($donation['created_at']); ?></small>
                                </div>
                                <div class="text-end">
                                    <span class="fw-bold text-success">
                                        <?php echo formatAmount($donation['amount'], $donation['currency']); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Footer -->
    <footer id="contact" class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5><?php echo htmlspecialchars($orgName); ?></h5>
                    <p>نعمل على نشر الخير ومساعدة المحتاجين في جميع أنحاء المملكة</p>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5>معلومات الاتصال</h5>
                    <?php if ($orgAddress): ?>
                        <div class="contact-info">
                            <i class="fas fa-map-marker-alt"></i>
                            <?php echo htmlspecialchars($orgAddress); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($orgPhone): ?>
                        <div class="contact-info">
                            <i class="fas fa-phone"></i>
                            <a href="tel:<?php echo htmlspecialchars($orgPhone); ?>">
                                <?php echo htmlspecialchars($orgPhone); ?>
                            </a>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($orgEmail): ?>
                        <div class="contact-info">
                            <i class="fas fa-envelope"></i>
                            <a href="mailto:<?php echo htmlspecialchars($orgEmail); ?>">
                                <?php echo htmlspecialchars($orgEmail); ?>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5>روابط مهمة</h5>
                    <div class="contact-info">
                        <i class="fas fa-qrcode"></i>
                        <a href="donor_transactions.php">استعلام عن التبرعات</a>
                    </div>
                    <div class="contact-info">
                        <i class="fas fa-cog"></i>
                        <a href="../admin/login.php">دخول الإدارة</a>
                    </div>
                </div>
            </div>
            
            <hr style="border-color: #34495e;">
            
            <div class="row">
                <div class="col-12 text-center">
                    <p>&copy; <?php echo date('Y'); ?> <?php echo htmlspecialchars($orgName); ?>. جميع الحقوق محفوظة.</p>
                    <p>تم تطوير النظام بواسطة فريق التطوير</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // Copy to clipboard function
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('تم نسخ رقم الحساب إلى الحافظة');
            }).catch(function() {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('تم نسخ رقم الحساب إلى الحافظة');
            });
        }
        
        // Navbar background on scroll
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(44, 62, 80, 1)';
            } else {
                navbar.style.background = 'rgba(44, 62, 80, 0.95)';
            }
        });
    </script>
</body>
</html>
