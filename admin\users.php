<?php
/**
 * صفحة إدارة المستخدمين
 * جمعية قوافل الخير
 */

define('SYSTEM_ACCESS', true);
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// التحقق من الصلاحية
requirePermission('users');

$action = $_GET['action'] ?? 'list';
$userId = $_GET['id'] ?? null;

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'create') {
        $data = [
            'username' => sanitize($_POST['username']),
            'password' => $_POST['password'],
            'full_name' => sanitize($_POST['full_name']),
            'email' => sanitize($_POST['email']),
            'phone' => sanitize($_POST['phone']),
            'role' => sanitize($_POST['role']),
            'permissions' => implode(',', $_POST['permissions'] ?? []),
            'status' => sanitize($_POST['status'])
        ];
        
        $result = $auth->createUser($data);
        
        if ($result['success']) {
            redirect('users.php', 'تم إنشاء المستخدم بنجاح', 'success');
        } else {
            setFlashMessage($result['message'], 'error');
        }
    } elseif ($action === 'edit' && $userId) {
        $data = [
            'username' => sanitize($_POST['username']),
            'full_name' => sanitize($_POST['full_name']),
            'email' => sanitize($_POST['email']),
            'phone' => sanitize($_POST['phone']),
            'role' => sanitize($_POST['role']),
            'permissions' => implode(',', $_POST['permissions'] ?? []),
            'status' => sanitize($_POST['status'])
        ];
        
        if (!empty($_POST['password'])) {
            $data['password'] = $_POST['password'];
        }
        
        $result = $auth->updateUser($userId, $data);
        
        if ($result['success']) {
            redirect('users.php', 'تم تحديث المستخدم بنجاح', 'success');
        } else {
            setFlashMessage($result['message'], 'error');
        }
    }
}

// حذف المستخدم
if ($action === 'delete' && $userId) {
    $result = $auth->deleteUser($userId);
    
    if ($result['success']) {
        redirect('users.php', 'تم حذف المستخدم بنجاح', 'success');
    } else {
        redirect('users.php', $result['message'], 'error');
    }
}

// الحصول على البيانات
if ($action === 'edit' && $userId) {
    $user = $db->selectOne("SELECT * FROM users WHERE id = :id", ['id' => $userId]);
    if (!$user) {
        redirect('users.php', 'المستخدم غير موجود', 'error');
    }
}

// قائمة المستخدمين
$users = $auth->getUsers();

$pageTitle = 'إدارة المستخدمين';
include 'includes/header.php';
?>

<?php if ($action === 'list'): ?>
    <!-- قائمة المستخدمين -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">قائمة المستخدمين</h6>
            <a href="users.php?action=create" class="btn btn-primary btn-sm">
                <i class="fas fa-plus"></i> إضافة مستخدم جديد
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered data-table" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>الرقم</th>
                            <th>اسم المستخدم</th>
                            <th>الاسم الكامل</th>
                            <th>البريد الإلكتروني</th>
                            <th>الدور</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th>العمليات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                            <tr>
                                <td><?php echo $user['id']; ?></td>
                                <td><?php echo htmlspecialchars($user['username']); ?></td>
                                <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                <td>
                                    <span class="badge badge-<?php echo $user['role'] === 'admin' ? 'danger' : ($user['role'] === 'employee' ? 'warning' : 'info'); ?>">
                                        <?php echo USER_ROLES[$user['role']]; ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge badge-<?php echo $user['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                        <?php echo $user['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                    </span>
                                </td>
                                <td><?php echo formatDate($user['created_at']); ?></td>
                                <td>
                                    <a href="users.php?action=edit&id=<?php echo $user['id']; ?>" 
                                       class="btn btn-sm btn-warning">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <?php if ($user['id'] != 1 && $user['id'] != $_SESSION['user_id']): ?>
                                        <a href="users.php?action=delete&id=<?php echo $user['id']; ?>" 
                                           class="btn btn-sm btn-danger"
                                           onclick="return confirmDelete('هل أنت متأكد من حذف هذا المستخدم؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

<?php elseif ($action === 'create' || $action === 'edit'): ?>
    <!-- نموذج إضافة/تعديل المستخدم -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <?php echo $action === 'create' ? 'إضافة مستخدم جديد' : 'تعديل المستخدم'; ?>
            </h6>
        </div>
        <div class="card-body">
            <form method="POST" action="">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="username" class="form-label">اسم المستخدم *</label>
                            <input type="text" class="form-control" id="username" name="username" 
                                   value="<?php echo htmlspecialchars($user['username'] ?? ''); ?>" 
                                   required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="full_name" class="form-label">الاسم الكامل *</label>
                            <input type="text" class="form-control" id="full_name" name="full_name" 
                                   value="<?php echo htmlspecialchars($user['full_name'] ?? ''); ?>" 
                                   required>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="text" class="form-control" id="phone" name="phone" 
                                   value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="password" class="form-label">
                                كلمة المرور <?php echo $action === 'create' ? '*' : '(اتركها فارغة إذا لم ترد تغييرها)'; ?>
                            </label>
                            <input type="password" class="form-control" id="password" name="password" 
                                   <?php echo $action === 'create' ? 'required' : ''; ?>>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="role" class="form-label">الدور *</label>
                            <select class="form-control" id="role" name="role" required>
                                <?php foreach (USER_ROLES as $roleKey => $roleName): ?>
                                    <option value="<?php echo $roleKey; ?>" 
                                            <?php echo ($user['role'] ?? '') === $roleKey ? 'selected' : ''; ?>>
                                        <?php echo $roleName; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">الصلاحيات</label>
                            <?php 
                            $userPermissions = isset($user['permissions']) ? 
                                explode(',', $user['permissions']) : [];
                            ?>
                            <?php foreach (PERMISSIONS as $permKey => $permName): ?>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" 
                                           id="perm_<?php echo $permKey; ?>" 
                                           name="permissions[]" 
                                           value="<?php echo $permKey; ?>"
                                           <?php echo in_array($permKey, $userPermissions) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="perm_<?php echo $permKey; ?>">
                                        <?php echo $permName; ?>
                                    </label>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="status" class="form-label">الحالة *</label>
                            <select class="form-control" id="status" name="status" required>
                                <option value="active" <?php echo ($user['status'] ?? 'active') === 'active' ? 'selected' : ''; ?>>
                                    نشط
                                </option>
                                <option value="inactive" <?php echo ($user['status'] ?? '') === 'inactive' ? 'selected' : ''; ?>>
                                    غير نشط
                                </option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        <?php echo $action === 'create' ? 'إضافة المستخدم' : 'تحديث المستخدم'; ?>
                    </button>
                    <a href="users.php" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
<?php endif; ?>

<script>
// إخفاء/إظهار الصلاحيات حسب الدور
document.getElementById('role').addEventListener('change', function() {
    const permissionsDiv = document.querySelector('.form-group:has([name="permissions[]"])').parentElement;
    
    if (this.value === 'admin') {
        permissionsDiv.style.display = 'none';
    } else {
        permissionsDiv.style.display = 'block';
    }
});

// تشغيل الدالة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const roleSelect = document.getElementById('role');
    if (roleSelect) {
        roleSelect.dispatchEvent(new Event('change'));
    }
});
</script>

<?php include 'includes/footer.php'; ?>
