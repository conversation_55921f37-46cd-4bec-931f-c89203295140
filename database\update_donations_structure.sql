-- تحديث هيكل جدول التبرعات ليتوافق مع المتطلبات الجديدة
-- نظام إدارة التبرعات - جمعية قوافل الخير

-- حذف الجدول القديم وإنشاء جديد
DROP TABLE IF EXISTS `donations`;

-- إن<PERSON><PERSON><PERSON> جدول التبرعات المحدث
CREATE TABLE `donations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `donation_type` enum('monthly_sponsorship','restricted','unrestricted') NOT NULL,
  
  -- حقول مشتركة
  `amount` decimal(10,2) NOT NULL,
  `currency` varchar(3) NOT NULL DEFAULT 'DZD',
  `payment_method` enum('cash','bank_account') NOT NULL,
  `registration_number` varchar(50) DEFAULT NULL,
  `withdrawal_number` varchar(50) DEFAULT NULL,
  `expense_voucher` varchar(50) DEFAULT NULL,
  `donation_date` date NOT NULL,
  
  -- حقول الكفالات الشهرية
  `sponsor_name` varchar(255) DEFAULT NULL,
  `sponsor_gender` enum('male','female') DEFAULT NULL,
  `sponsor_phone` varchar(20) DEFAULT NULL,
  `sponsor_state` varchar(100) DEFAULT NULL,
  `monthly_amount` decimal(10,2) DEFAULT NULL,
  `family_number` varchar(50) DEFAULT NULL,
  `sponsorship_month` int(2) DEFAULT NULL,
  `sponsorship_year` int(4) DEFAULT NULL,
  
  -- حقول التبرعات المقيدة وغير المقيدة
  `donor_name` varchar(255) DEFAULT NULL,
  `restriction_type` enum('family','project') DEFAULT NULL,
  `family_file_number` varchar(50) DEFAULT NULL,
  `project_type` varchar(100) DEFAULT NULL,
  
  -- حقول إضافية
  `receipt_number` varchar(50) DEFAULT NULL,
  `serial_number` varchar(8) UNIQUE NOT NULL,
  `notes` text DEFAULT NULL,
  `receipt_path` varchar(255) DEFAULT NULL,
  `qr_code` varchar(255) DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  FOREIGN KEY (`created_by`) REFERENCES `users`(`id`),
  INDEX `idx_serial_number` (`serial_number`),
  INDEX `idx_donation_type` (`donation_type`),
  INDEX `idx_donation_date` (`donation_date`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول المشاريع
CREATE TABLE `projects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `project_name` varchar(200) NOT NULL,
  `project_code` varchar(50) UNIQUE NOT NULL,
  `description` text DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  INDEX `idx_project_code` (`project_code`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج المشاريع الأساسية
INSERT INTO `projects` (`project_name`, `project_code`, `description`) VALUES
('شراء البيوت', 'HOUSE_BUY', 'مشروع شراء البيوت للأيتام والأرامل'),
('ترميم بيوت الأيتام', 'HOUSE_REPAIR', 'ترميم وإصلاح بيوت الأيتام'),
('تأثيث بيوت الأيتام', 'HOUSE_FURNISH', 'تأثيث بيوت الأيتام بالأثاث الضروري'),
('تسديد حقوق الكراء', 'RENT_PAY', 'تسديد إيجارات المنازل للعائلات المحتاجة'),
('تفريج الكربات', 'RELIEF', 'تفريج الكربات والمساعدة في الأزمات'),
('الحالات الصحية', 'HEALTH', 'المساعدة في العلاج والحالات الصحية'),
('الترفيه', 'ENTERTAINMENT', 'أنشطة ترفيهية للأيتام'),
('جبر خواطر الأيتام', 'ORPHAN_CARE', 'برامج جبر خواطر الأيتام'),
('قفة رمضان', 'RAMADAN_BASKET', 'توزيع قفف رمضان على العائلات'),
('كسوة الأيتام', 'ORPHAN_CLOTHES', 'كسوة الأيتام والملابس'),
('عيد الأضحى', 'EID_ADHA', 'مساعدات عيد الأضحى'),
('الدخول المدرسي', 'SCHOOL_ENTRY', 'مساعدات الدخول المدرسي'),
('اليتيم المتميز', 'OUTSTANDING_ORPHAN', 'برنامج تكريم اليتيم المتميز'),
('الأرملة المنتجة', 'PRODUCTIVE_WIDOW', 'برنامج دعم الأرملة المنتجة'),
('الأرملة المثالية', 'IDEAL_WIDOW', 'برنامج تكريم الأرملة المثالية');

-- إنشاء جدول الولايات الجزائرية
CREATE TABLE `states` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `state_name` varchar(100) NOT NULL,
  `state_code` varchar(10) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  INDEX `idx_state_code` (`state_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج الولايات الجزائرية
INSERT INTO `states` (`state_name`, `state_code`) VALUES
('أدرار', '01'),
('الشلف', '02'),
('الأغواط', '03'),
('أم البواقي', '04'),
('باتنة', '05'),
('بجاية', '06'),
('بسكرة', '07'),
('بشار', '08'),
('البليدة', '09'),
('البويرة', '10'),
('تمنراست', '11'),
('تبسة', '12'),
('تلمسان', '13'),
('تيارت', '14'),
('تيزي وزو', '15'),
('الجزائر', '16'),
('الجلفة', '17'),
('جيجل', '18'),
('سطيف', '19'),
('سعيدة', '20'),
('سكيكدة', '21'),
('سيدي بلعباس', '22'),
('عنابة', '23'),
('قالمة', '24'),
('قسنطينة', '25'),
('المدية', '26'),
('مستغانم', '27'),
('المسيلة', '28'),
('معسكر', '29'),
('ورقلة', '30'),
('وهران', '31'),
('البيض', '32'),
('إليزي', '33'),
('برج بوعريريج', '34'),
('بومرداس', '35'),
('الطارف', '36'),
('تندوف', '37'),
('تيسمسيلت', '38'),
('الوادي', '39'),
('خنشلة', '40'),
('سوق أهراس', '41'),
('تيبازة', '42'),
('ميلة', '43'),
('عين الدفلى', '44'),
('النعامة', '45'),
('عين تموشنت', '46'),
('غرداية', '47'),
('غليزان', '48'),
('تيميمون', '49'),
('برج باجي مختار', '50'),
('أولاد جلال', '51'),
('بني عباس', '52'),
('عين صالح', '53'),
('عين قزام', '54'),
('تقرت', '55'),
('جانت', '56'),
('المغير', '57'),
('المنيعة', '58');

-- تحديث إعدادات النظام
UPDATE `settings` SET `setting_value` = 'DZD' WHERE `setting_key` = 'default_currency';
INSERT IGNORE INTO `settings` (`setting_key`, `setting_value`, `description`) VALUES
('default_currency', 'DZD', 'العملة الافتراضية'),
('expense_voucher_start', '1', 'رقم البداية لسندات الصرف'),
('registration_color_cash', '#90EE90', 'لون تسجيل القيم النقدية (أخضر فاتح)'),
('registration_color_bank', '#ADD8E6', 'لون تسجيل القيم البنكية (أزرق فاتح)');
