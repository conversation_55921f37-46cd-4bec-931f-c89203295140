<?php
/**
 * تحديث سريع لقاعدة البيانات
 * إضافة الأعمدة المفقودة
 */

define('SYSTEM_ACCESS', true);
require_once 'includes/config.php';
require_once 'includes/database.php';

echo "<h2>تحديث سريع لقاعدة البيانات</h2>";

try {
    // فحص الأعمدة الموجودة
    $columns = $db->select("SHOW COLUMNS FROM donations");
    $existingColumns = array_column($columns, 'Field');
    
    echo "<h3>الأعمدة الموجودة حالياً:</h3>";
    echo "<ul>";
    foreach ($existingColumns as $col) {
        echo "<li>$col</li>";
    }
    echo "</ul>";
    
    // الأعمدة المطلوبة
    $requiredColumns = [
        'registration_number' => "ADD COLUMN `registration_number` varchar(50) DEFAULT NULL",
        'withdrawal_number' => "ADD COLUMN `withdrawal_number` varchar(50) DEFAULT NULL", 
        'expense_voucher' => "ADD COLUMN `expense_voucher` varchar(50) DEFAULT NULL",
        'donation_date' => "ADD COLUMN `donation_date` date NOT NULL DEFAULT (CURRENT_DATE)",
        'sponsor_name' => "ADD COLUMN `sponsor_name` varchar(255) DEFAULT NULL",
        'sponsor_gender' => "ADD COLUMN `sponsor_gender` enum('male','female') DEFAULT NULL",
        'sponsor_phone' => "ADD COLUMN `sponsor_phone` varchar(20) DEFAULT NULL",
        'sponsor_state' => "ADD COLUMN `sponsor_state` varchar(100) DEFAULT NULL",
        'monthly_amount' => "ADD COLUMN `monthly_amount` decimal(10,2) DEFAULT NULL",
        'family_number' => "ADD COLUMN `family_number` varchar(50) DEFAULT NULL",
        'sponsorship_month' => "ADD COLUMN `sponsorship_month` int(2) DEFAULT NULL",
        'sponsorship_year' => "ADD COLUMN `sponsorship_year` int(4) DEFAULT NULL",
        'donor_name' => "ADD COLUMN `donor_name` varchar(255) DEFAULT NULL",
        'restriction_type' => "ADD COLUMN `restriction_type` enum('family','project') DEFAULT NULL",
        'family_file_number' => "ADD COLUMN `family_file_number` varchar(50) DEFAULT NULL",
        'project_type' => "ADD COLUMN `project_type` varchar(100) DEFAULT NULL"
    ];
    
    echo "<h3>إضافة الأعمدة المفقودة:</h3>";
    
    foreach ($requiredColumns as $columnName => $alterSQL) {
        if (!in_array($columnName, $existingColumns)) {
            try {
                $db->query("ALTER TABLE donations $alterSQL");
                echo "<p style='color: green;'>✅ تم إضافة العمود: $columnName</p>";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ فشل في إضافة العمود $columnName: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ العمود موجود مسبقاً: $columnName</p>";
        }
    }
    
    // تحديث طرق الدفع
    try {
        $db->query("ALTER TABLE donations MODIFY COLUMN payment_method enum('cash','bank_account') NOT NULL DEFAULT 'cash'");
        echo "<p style='color: green;'>✅ تم تحديث طرق الدفع</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ تحديث طرق الدفع: " . $e->getMessage() . "</p>";
    }
    
    // تحديث العملة الافتراضية
    try {
        $db->query("ALTER TABLE donations MODIFY COLUMN currency varchar(3) NOT NULL DEFAULT 'DZD'");
        echo "<p style='color: green;'>✅ تم تحديث العملة الافتراضية</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ تحديث العملة: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
    echo "<h3 style='color: green;'>✅ تم الانتهاء من التحديث!</h3>";
    echo "<p><a href='admin/donations.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب لنظام التبرعات</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في التحديث: " . $e->getMessage() . "</p>";
}
?>
