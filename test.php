<?php
/**
 * ملف اختبار النظام
 */

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>اختبار النظام</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap' rel='stylesheet'>";
echo "<style>body{font-family:'Cairo',sans-serif;padding:20px;}</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1 class='text-center mb-4'>اختبار نظام إدارة التبرعات</h1>";

echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<div class='card'>";
echo "<div class='card-header'><h5>معلومات PHP</h5></div>";
echo "<div class='card-body'>";
echo "<p><strong>إصدار PHP:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>الخادم:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p><strong>المجلد الحالي:</strong> " . __DIR__ . "</p>";
echo "<p><strong>التاريخ:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<div class='card'>";
echo "<div class='card-header'><h5>الامتدادات المطلوبة</h5></div>";
echo "<div class='card-body'>";

$extensions = [
    'PDO' => extension_loaded('pdo'),
    'PDO MySQL' => extension_loaded('pdo_mysql'),
    'GD' => extension_loaded('gd'),
    'mbstring' => extension_loaded('mbstring'),
    'JSON' => extension_loaded('json'),
    'cURL' => extension_loaded('curl')
];

foreach ($extensions as $ext => $loaded) {
    $status = $loaded ? '<span class="text-success">✓ متوفر</span>' : '<span class="text-danger">✗ غير متوفر</span>';
    echo "<p><strong>$ext:</strong> $status</p>";
}

echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='row mt-4'>";
echo "<div class='col-12'>";
echo "<div class='card'>";
echo "<div class='card-header'><h5>اختبار المجلدات</h5></div>";
echo "<div class='card-body'>";

$directories = [
    'uploads' => is_dir('uploads'),
    'uploads/receipts' => is_dir('uploads/receipts'),
    'uploads/backups' => is_dir('uploads/backups'),
    'logs' => is_dir('logs'),
    'includes' => is_dir('includes'),
    'admin' => is_dir('admin'),
    'public' => is_dir('public')
];

foreach ($directories as $dir => $exists) {
    $status = $exists ? '<span class="text-success">✓ موجود</span>' : '<span class="text-danger">✗ غير موجود</span>';
    echo "<p><strong>$dir:</strong> $status</p>";
}

echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='row mt-4'>";
echo "<div class='col-12 text-center'>";
echo "<div class='card'>";
echo "<div class='card-body'>";
echo "<h5>الخطوات التالية</h5>";
echo "<div class='d-grid gap-2 d-md-flex justify-content-md-center'>";
echo "<a href='setup.php' class='btn btn-primary'>بدء الإعداد</a>";
echo "<a href='index.php' class='btn btn-success'>الصفحة الرئيسية</a>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
