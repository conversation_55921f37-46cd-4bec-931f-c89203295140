<?php
/**
 * صفحة التقارير والإحصائيات
 * جمعية قوافل الخير
 */

define('SYSTEM_ACCESS', true);
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// التحقق من الصلاحية
requirePermission('reports');

$reportType = $_GET['type'] ?? 'overview';
$dateFrom = sanitize($_GET['date_from'] ?? date('Y-m-01'));
$dateTo = sanitize($_GET['date_to'] ?? date('Y-m-d'));
$donationType = sanitize($_GET['donation_type'] ?? '');

// تصدير التقرير
if (isset($_GET['export'])) {
    $exportType = $_GET['export'];
    
    if ($exportType === 'pdf') {
        require_once '../includes/pdf_generator.php';
        $pdfGenerator = new PDFGenerator();
        
        // إعداد بيانات التقرير حسب النوع
        $reportData = getReportData($reportType, $dateFrom, $dateTo, $donationType);
        $filename = $pdfGenerator->generateReport(
            $reportData['title'],
            $reportData['data'],
            $reportData['columns']
        );
        
        if ($filename) {
            header('Content-Type: application/pdf');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            readfile(UPLOADS_PATH . '/' . $filename);
            exit;
        }
    } elseif ($exportType === 'excel') {
        exportToExcel($reportType, $dateFrom, $dateTo, $donationType);
    }
}

// دالة الحصول على بيانات التقرير
function getReportData($type, $dateFrom, $dateTo, $donationType) {
    global $db;
    
    $whereConditions = ["DATE(d.created_at) BETWEEN :date_from AND :date_to"];
    $params = ['date_from' => $dateFrom, 'date_to' => $dateTo];
    
    if ($donationType) {
        $whereConditions[] = "d.donation_type = :donation_type";
        $params['donation_type'] = $donationType;
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    switch ($type) {
        case 'donations':
            $data = $db->select("
                SELECT d.serial_number, don.name as donor_name, d.donation_type, 
                       d.amount, d.currency, d.payment_method, d.created_at
                FROM donations d
                LEFT JOIN donors don ON d.donor_id = don.id
                WHERE $whereClause
                ORDER BY d.created_at DESC
            ", $params);
            
            return [
                'title' => 'تقرير التبرعات من ' . $dateFrom . ' إلى ' . $dateTo,
                'data' => array_map(function($row) {
                    return [
                        $row['serial_number'],
                        $row['donor_name'],
                        DONATION_TYPES[$row['donation_type']],
                        formatAmount($row['amount'], $row['currency']),
                        PAYMENT_METHODS[$row['payment_method']],
                        formatDate($row['created_at'])
                    ];
                }, $data),
                'columns' => ['الرقم التسلسلي', 'اسم المتبرع', 'نوع التبرع', 'المبلغ', 'طريقة الدفع', 'التاريخ']
            ];
            
        case 'donors':
            $data = $db->select("
                SELECT don.name, don.phone, don.email, 
                       COUNT(d.id) as donations_count,
                       SUM(d.amount) as total_amount,
                       MAX(d.created_at) as last_donation
                FROM donors don
                LEFT JOIN donations d ON don.id = d.donor_id AND $whereClause
                GROUP BY don.id
                HAVING donations_count > 0
                ORDER BY total_amount DESC
            ", $params);
            
            return [
                'title' => 'تقرير المتبرعين من ' . $dateFrom . ' إلى ' . $dateTo,
                'data' => array_map(function($row) {
                    return [
                        $row['name'],
                        $row['phone'] ?: 'غير محدد',
                        $row['email'] ?: 'غير محدد',
                        number_format($row['donations_count']),
                        formatAmount($row['total_amount']),
                        formatDate($row['last_donation'])
                    ];
                }, $data),
                'columns' => ['اسم المتبرع', 'الهاتف', 'البريد الإلكتروني', 'عدد التبرعات', 'إجمالي المبلغ', 'آخر تبرع']
            ];
            
        default:
            return [
                'title' => 'تقرير عام',
                'data' => [],
                'columns' => []
            ];
    }
}

// دالة تصدير Excel
function exportToExcel($type, $dateFrom, $dateTo, $donationType) {
    require_once '../vendor/autoload.php';
    
    $reportData = getReportData($type, $dateFrom, $dateTo, $donationType);
    
    $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    
    // إعداد العناوين
    $sheet->setTitle('تقرير');
    $sheet->setCellValue('A1', $reportData['title']);
    $sheet->mergeCells('A1:' . chr(65 + count($reportData['columns']) - 1) . '1');
    
    // إعداد أعمدة الجدول
    $col = 'A';
    foreach ($reportData['columns'] as $column) {
        $sheet->setCellValue($col . '3', $column);
        $col++;
    }
    
    // إدراج البيانات
    $row = 4;
    foreach ($reportData['data'] as $dataRow) {
        $col = 'A';
        foreach ($dataRow as $cell) {
            $sheet->setCellValue($col . $row, $cell);
            $col++;
        }
        $row++;
    }
    
    // تنسيق الملف
    $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
    $sheet->getStyle('A3:' . chr(65 + count($reportData['columns']) - 1) . '3')->getFont()->setBold(true);
    
    // حفظ الملف
    $filename = 'report_' . $type . '_' . date('Y-m-d_H-i-s') . '.xlsx';
    $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
    
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    $writer->save('php://output');
    exit;
}

// الحصول على الإحصائيات العامة
$stats = [];

// إحصائيات الفترة المحددة
$periodStats = $db->selectOne("
    SELECT 
        COUNT(*) as total_donations,
        SUM(amount) as total_amount,
        AVG(amount) as avg_amount
    FROM donations 
    WHERE DATE(created_at) BETWEEN :date_from AND :date_to
", ['date_from' => $dateFrom, 'date_to' => $dateTo]);

$stats['period'] = $periodStats;

// إحصائيات حسب نوع التبرع
$typeStats = $db->select("
    SELECT 
        donation_type,
        COUNT(*) as count,
        SUM(amount) as total
    FROM donations 
    WHERE DATE(created_at) BETWEEN :date_from AND :date_to
    GROUP BY donation_type
", ['date_from' => $dateFrom, 'date_to' => $dateTo]);

$stats['types'] = $typeStats;

// إحصائيات حسب طريقة الدفع
$paymentStats = $db->select("
    SELECT 
        payment_method,
        COUNT(*) as count,
        SUM(amount) as total
    FROM donations 
    WHERE DATE(created_at) BETWEEN :date_from AND :date_to
    GROUP BY payment_method
", ['date_from' => $dateFrom, 'date_to' => $dateTo]);

$stats['payments'] = $paymentStats;

// أكثر المتبرعين نشاطاً
$topDonors = $db->select("
    SELECT 
        don.name,
        COUNT(d.id) as donations_count,
        SUM(d.amount) as total_amount
    FROM donors don
    LEFT JOIN donations d ON don.id = d.donor_id
    WHERE DATE(d.created_at) BETWEEN :date_from AND :date_to
    GROUP BY don.id
    ORDER BY total_amount DESC
    LIMIT 10
", ['date_from' => $dateFrom, 'date_to' => $dateTo]);

$stats['top_donors'] = $topDonors;

// إحصائيات شهرية
$monthlyStats = $db->select("
    SELECT 
        DATE_FORMAT(created_at, '%Y-%m') as month,
        COUNT(*) as count,
        SUM(amount) as total
    FROM donations 
    WHERE DATE(created_at) BETWEEN :date_from AND :date_to
    GROUP BY DATE_FORMAT(created_at, '%Y-%m')
    ORDER BY month
", ['date_from' => $dateFrom, 'date_to' => $dateTo]);

$stats['monthly'] = $monthlyStats;

$pageTitle = 'التقارير والإحصائيات';
include 'includes/header.php';
?>

<!-- فلاتر التقرير -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">فلاتر التقرير</h6>
    </div>
    <div class="card-body">
        <form method="GET" action="">
            <div class="row">
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="type">نوع التقرير</label>
                        <select class="form-control" id="type" name="type">
                            <option value="overview" <?php echo $reportType === 'overview' ? 'selected' : ''; ?>>
                                نظرة عامة
                            </option>
                            <option value="donations" <?php echo $reportType === 'donations' ? 'selected' : ''; ?>>
                                تقرير التبرعات
                            </option>
                            <option value="donors" <?php echo $reportType === 'donors' ? 'selected' : ''; ?>>
                                تقرير المتبرعين
                            </option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="date_from">من تاريخ</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" 
                               value="<?php echo $dateFrom; ?>">
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="date_to">إلى تاريخ</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" 
                               value="<?php echo $dateTo; ?>">
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="donation_type">نوع التبرع</label>
                        <select class="form-control" id="donation_type" name="donation_type">
                            <option value="">الكل</option>
                            <?php foreach (DONATION_TYPES as $key => $value): ?>
                                <option value="<?php echo $key; ?>" 
                                        <?php echo $donationType === $key ? 'selected' : ''; ?>>
                                    <?php echo $value; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <div class="d-flex">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search"></i> عرض التقرير
                            </button>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['export' => 'pdf'])); ?>" 
                               class="btn btn-danger me-2">
                                <i class="fas fa-file-pdf"></i> PDF
                            </a>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['export' => 'excel'])); ?>" 
                               class="btn btn-success">
                                <i class="fas fa-file-excel"></i> Excel
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- الإحصائيات السريعة -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            إجمالي التبرعات (الفترة)
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo number_format($stats['period']['total_donations'] ?? 0); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-donate fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            إجمالي المبلغ (الفترة)
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo formatAmount($stats['period']['total_amount'] ?? 0); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            متوسط التبرع
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo formatAmount($stats['period']['avg_amount'] ?? 0); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            الفترة المحددة
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo formatDate($dateFrom) . ' - ' . formatDate($dateTo); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- التبرعات حسب النوع -->
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">التبرعات حسب النوع</h6>
            </div>
            <div class="card-body">
                <canvas id="typeChart"></canvas>
            </div>
        </div>
    </div>

    <!-- التبرعات حسب طريقة الدفع -->
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">التبرعات حسب طريقة الدفع</h6>
            </div>
            <div class="card-body">
                <canvas id="paymentChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- أكثر المتبرعين نشاطاً -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">أكثر المتبرعين نشاطاً (أعلى 10)</h6>
    </div>
    <div class="card-body">
        <?php if (!empty($stats['top_donors'])): ?>
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>الترتيب</th>
                            <th>اسم المتبرع</th>
                            <th>عدد التبرعات</th>
                            <th>إجمالي المبلغ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($stats['top_donors'] as $index => $donor): ?>
                            <tr>
                                <td><?php echo $index + 1; ?></td>
                                <td><?php echo htmlspecialchars($donor['name']); ?></td>
                                <td><?php echo number_format($donor['donations_count']); ?></td>
                                <td><?php echo formatAmount($donor['total_amount']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <p class="text-muted text-center">لا توجد بيانات للفترة المحددة</p>
        <?php endif; ?>
    </div>
</div>

<!-- الإحصائيات الشهرية -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">الإحصائيات الشهرية</h6>
    </div>
    <div class="card-body">
        <canvas id="monthlyChart"></canvas>
    </div>
</div>

<script>
// رسم بياني للتبرعات حسب النوع
const typeCtx = document.getElementById('typeChart').getContext('2d');
const typeChart = new Chart(typeCtx, {
    type: 'doughnut',
    data: {
        labels: [
            <?php foreach ($stats['types'] as $type): ?>
                '<?php echo DONATION_TYPES[$type['donation_type']]; ?>',
            <?php endforeach; ?>
        ],
        datasets: [{
            data: [
                <?php foreach ($stats['types'] as $type): ?>
                    <?php echo $type['total']; ?>,
                <?php endforeach; ?>
            ],
            backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b']
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// رسم بياني للتبرعات حسب طريقة الدفع
const paymentCtx = document.getElementById('paymentChart').getContext('2d');
const paymentChart = new Chart(paymentCtx, {
    type: 'pie',
    data: {
        labels: [
            <?php foreach ($stats['payments'] as $payment): ?>
                '<?php echo PAYMENT_METHODS[$payment['payment_method']]; ?>',
            <?php endforeach; ?>
        ],
        datasets: [{
            data: [
                <?php foreach ($stats['payments'] as $payment): ?>
                    <?php echo $payment['total']; ?>,
                <?php endforeach; ?>
            ],
            backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e']
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// رسم بياني للإحصائيات الشهرية
const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
const monthlyChart = new Chart(monthlyCtx, {
    type: 'line',
    data: {
        labels: [
            <?php foreach ($stats['monthly'] as $month): ?>
                '<?php echo $month['month']; ?>',
            <?php endforeach; ?>
        ],
        datasets: [{
            label: 'عدد التبرعات',
            data: [
                <?php foreach ($stats['monthly'] as $month): ?>
                    <?php echo $month['count']; ?>,
                <?php endforeach; ?>
            ],
            borderColor: '#4e73df',
            backgroundColor: 'rgba(78, 115, 223, 0.1)',
            yAxisID: 'y'
        }, {
            label: 'إجمالي المبلغ',
            data: [
                <?php foreach ($stats['monthly'] as $month): ?>
                    <?php echo $month['total']; ?>,
                <?php endforeach; ?>
            ],
            borderColor: '#1cc88a',
            backgroundColor: 'rgba(28, 200, 138, 0.1)',
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'right'
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'left',
                grid: {
                    drawOnChartArea: false
                }
            }
        }
    }
});
</script>

<?php include 'includes/footer.php'; ?>
