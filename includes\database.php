<?php
/**
 * ملف اتصال قاعدة البيانات
 * جمعية قوافل الخير
 */

// منع الوصول المباشر
if (!defined('SYSTEM_ACCESS')) {
    die('Access denied');
}

class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
            ];
            
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            if (DEBUG_MODE) {
                die("Database connection failed: " . $e->getMessage());
            } else {
                die("Database connection failed");
            }
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            $this->logError($e->getMessage(), $sql, $params);
            throw $e;
        }
    }
    
    public function select($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    public function selectOne($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $this->query($sql, $data);
        
        return $this->connection->lastInsertId();
    }
    
    public function update($table, $data, $where, $whereParams = []) {
        $setClause = [];
        foreach (array_keys($data) as $key) {
            $setClause[] = "{$key} = :{$key}";
        }
        $setClause = implode(', ', $setClause);
        
        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        $params = array_merge($data, $whereParams);
        
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    public function count($table, $where = '1=1', $params = []) {
        $sql = "SELECT COUNT(*) as count FROM {$table} WHERE {$where}";
        $result = $this->selectOne($sql, $params);
        return $result['count'];
    }
    
    public function exists($table, $where, $params = []) {
        return $this->count($table, $where, $params) > 0;
    }
    
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }
    
    public function commit() {
        return $this->connection->commit();
    }
    
    public function rollback() {
        return $this->connection->rollback();
    }
    
    public function getLastInsertId() {
        return $this->connection->lastInsertId();
    }
    
    private function logError($message, $sql = '', $params = []) {
        if (LOG_ERRORS) {
            $logMessage = date('[Y-m-d H:i:s] ') . "Database Error: {$message}\n";
            if ($sql) {
                $logMessage .= "SQL: {$sql}\n";
            }
            if (!empty($params)) {
                $logMessage .= "Params: " . json_encode($params) . "\n";
            }
            $logMessage .= "---\n";
            
            error_log($logMessage, 3, ERROR_LOG_FILE);
        }
    }
    
    public function backup($filename = null) {
        if (!$filename) {
            $filename = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
        }
        
        $backupPath = BACKUPS_PATH . '/' . $filename;
        
        // استخدام mysqldump لإنشاء النسخة الاحتياطية
        $command = sprintf(
            'mysqldump --host=%s --user=%s --password=%s %s > %s',
            DB_HOST,
            DB_USER,
            DB_PASS,
            DB_NAME,
            $backupPath
        );
        
        exec($command, $output, $returnCode);
        
        if ($returnCode === 0 && file_exists($backupPath)) {
            return $filename;
        }
        
        return false;
    }
    
    public function restore($filename) {
        $backupPath = BACKUPS_PATH . '/' . $filename;
        
        if (!file_exists($backupPath)) {
            return false;
        }
        
        // استخدام mysql لاستعادة النسخة الاحتياطية
        $command = sprintf(
            'mysql --host=%s --user=%s --password=%s %s < %s',
            DB_HOST,
            DB_USER,
            DB_PASS,
            DB_NAME,
            $backupPath
        );
        
        exec($command, $output, $returnCode);
        
        return $returnCode === 0;
    }
    
    public function getTableSize($table) {
        $sql = "SELECT 
                    table_name AS 'table_name',
                    round(((data_length + index_length) / 1024 / 1024), 2) AS 'size_mb'
                FROM information_schema.TABLES 
                WHERE table_schema = :database AND table_name = :table";
        
        $result = $this->selectOne($sql, [
            'database' => DB_NAME,
            'table' => $table
        ]);
        
        return $result ? $result['size_mb'] : 0;
    }
    
    public function getDatabaseSize() {
        $sql = "SELECT 
                    round(sum(data_length + index_length) / 1024 / 1024, 2) AS 'size_mb'
                FROM information_schema.tables 
                WHERE table_schema = :database";
        
        $result = $this->selectOne($sql, ['database' => DB_NAME]);
        return $result ? $result['size_mb'] : 0;
    }
}

// إنشاء اتصال قاعدة البيانات العام
$db = Database::getInstance();
?>
