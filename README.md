# نظام إدارة التبرعات - جمعية قوافل الخير

## هيكل المشروع

```
donation_system/
├── admin/                  # لوحة التحكم الإدارية
│   ├── index.php          # الصفحة الرئيسية للإدارة
│   ├── login.php          # تسجيل الدخول
│   ├── logout.php         # تسجيل الخروج
│   ├── dashboard.php      # لوحة المعلومات
│   ├── users.php          # إدارة المستخدمين
│   ├── donations.php      # إدارة التبرعات
│   ├── reports.php        # التقارير والإحصائيات
│   ├── backup.php         # النسخ الاحتياطي
│   └── assets/            # ملفات CSS/JS/Images للإدارة
├── includes/              # الملفات المشتركة
│   ├── config.php         # إعدادات النظام
│   ├── database.php       # اتصال قاعدة البيانات
│   ├── functions.php      # الدوال المساعدة
│   ├── auth.php           # نظام المصادقة
│   └── pdf_generator.php  # توليد ملفات PDF
├── public/                # الواجهة العامة
│   ├── index.php          # الصفحة الرئيسية العامة
│   ├── donor_transactions.php # صفحة معاملات المتبرع
│   └── assets/            # ملفات CSS/JS/Images العامة
├── uploads/               # ملفات التحميل
│   ├── receipts/          # إيصالات PDF
│   └── backups/           # النسخ الاحتياطية
├── vendor/                # مكتبات PHP الخارجية
└── database/              # ملفات قاعدة البيانات
    └── setup.sql          # إعداد قاعدة البيانات
```

## المتطلبات

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache أو Nginx
- مكتبات PHP: PDO, GD, mbstring

## التنصيب

1. انسخ الملفات إلى مجلد الويب
2. قم بتشغيل ملف database/setup.sql في قاعدة البيانات
3. عدّل إعدادات قاعدة البيانات في includes/config.php
4. تأكد من صلاحيات الكتابة على مجلد uploads/

## بيانات الدخول الافتراضية

- المستخدم: admin
- كلمة المرور: admin123

## الميزات

- إدارة التبرعات بثلاثة أنواع
- توليد إيصالات PDF مع QR Code
- نظام صلاحيات متعدد المستويات
- تقارير وإحصائيات شاملة
- نسخ احتياطي واستعادة
- واجهة عامة للجمعية
