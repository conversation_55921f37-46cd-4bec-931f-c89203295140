<?php
/**
 * الدوال المساعدة للنظام
 * جمعية قوافل الخير
 */

// منع الوصول المباشر
if (!defined('SYSTEM_ACCESS')) {
    die('Access denied');
}

/**
 * تنظيف البيانات المدخلة
 */
function sanitize($data) {
    if (is_array($data)) {
        return array_map('sanitize', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * التحقق من صحة رقم الهاتف السعودي
 */
function isValidSaudiPhone($phone) {
    $pattern = '/^(\+966|966|0)?[5][0-9]{8}$/';
    return preg_match($pattern, $phone);
}

/**
 * تشفير كلمة المرور
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * التحقق من كلمة المرور
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * توليد رمز عشوائي
 */
function generateRandomCode($length = 8) {
    $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $code = '';
    for ($i = 0; $i < $length; $i++) {
        $code .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $code;
}

/**
 * توليد رقم تسلسلي للإيصال
 */
function generateSerialNumber() {
    global $db;
    
    // الحصول على الرقم التالي من الإعدادات
    $setting = $db->selectOne("SELECT setting_value FROM settings WHERE setting_key = 'next_serial'");
    $nextSerial = $setting ? $setting['setting_value'] : '10000001';
    
    // تحديث الرقم التالي
    $newSerial = str_pad((int)$nextSerial + 1, 8, '0', STR_PAD_LEFT);
    $db->update('settings', 
        ['setting_value' => $newSerial], 
        'setting_key = :key', 
        ['key' => 'next_serial']
    );
    
    return $nextSerial;
}

/**
 * توليد كود المتبرع
 */
function generateDonorCode() {
    global $db;
    
    do {
        $code = 'D' . date('Y') . generateRandomCode(6);
        $exists = $db->exists('donors', 'donor_code = :code', ['code' => $code]);
    } while ($exists);
    
    return $code;
}

/**
 * تنسيق المبلغ
 */
function formatAmount($amount, $currency = 'SAR') {
    return number_format($amount, 2) . ' ' . $currency;
}

/**
 * تنسيق التاريخ
 */
function formatDate($date, $format = DISPLAY_DATE_FORMAT) {
    if (!$date) return '';
    
    $timestamp = is_numeric($date) ? $date : strtotime($date);
    return date($format, $timestamp);
}

/**
 * تنسيق التاريخ والوقت
 */
function formatDateTime($datetime, $format = DISPLAY_DATETIME_FORMAT) {
    if (!$datetime) return '';
    
    $timestamp = is_numeric($datetime) ? $datetime : strtotime($datetime);
    return date($format, $timestamp);
}

/**
 * تحويل التاريخ الهجري (اختياري)
 */
function toHijri($date) {
    // يمكن إضافة مكتبة تحويل التاريخ الهجري هنا
    return formatDate($date);
}

/**
 * إعادة توجيه الصفحة
 */
function redirect($url, $message = '', $type = 'info') {
    if ($message) {
        setFlashMessage($message, $type);
    }
    header("Location: $url");
    exit;
}

/**
 * تعيين رسالة مؤقتة
 */
function setFlashMessage($message, $type = 'info') {
    if (!isset($_SESSION['flash_messages'])) {
        $_SESSION['flash_messages'] = [];
    }
    $_SESSION['flash_messages'][] = ['message' => $message, 'type' => $type];
}

/**
 * الحصول على الرسائل المؤقتة
 */
function getFlashMessages() {
    $messages = $_SESSION['flash_messages'] ?? [];
    unset($_SESSION['flash_messages']);
    return $messages;
}

/**
 * تسجيل العمليات
 */
function logActivity($action, $table = null, $recordId = null, $oldValues = null, $newValues = null) {
    global $db;
    
    if (!isset($_SESSION['user_id'])) return;
    
    $data = [
        'user_id' => $_SESSION['user_id'],
        'action' => $action,
        'table_name' => $table,
        'record_id' => $recordId,
        'old_values' => $oldValues ? json_encode($oldValues) : null,
        'new_values' => $newValues ? json_encode($newValues) : null,
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
    ];
    
    $db->insert('activity_log', $data);
}

/**
 * التحقق من رفع الملف
 */
function validateFileUpload($file, $allowedTypes = ALLOWED_EXTENSIONS, $maxSize = MAX_FILE_SIZE) {
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'خطأ في رفع الملف'];
    }
    
    if ($file['size'] > $maxSize) {
        return ['success' => false, 'message' => 'حجم الملف كبير جداً'];
    }
    
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($extension, $allowedTypes)) {
        return ['success' => false, 'message' => 'نوع الملف غير مسموح'];
    }
    
    return ['success' => true];
}

/**
 * رفع الملف
 */
function uploadFile($file, $directory, $newName = null) {
    $validation = validateFileUpload($file);
    if (!$validation['success']) {
        return $validation;
    }
    
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $filename = $newName ? $newName . '.' . $extension : uniqid() . '.' . $extension;
    $targetPath = $directory . '/' . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $targetPath)) {
        return ['success' => true, 'filename' => $filename, 'path' => $targetPath];
    }
    
    return ['success' => false, 'message' => 'فشل في حفظ الملف'];
}

/**
 * حذف الملف
 */
function deleteFile($path) {
    if (file_exists($path)) {
        return unlink($path);
    }
    return true;
}

/**
 * الحصول على إعداد النظام
 */
function getSetting($key, $default = null) {
    global $db;
    
    $setting = $db->selectOne("SELECT setting_value FROM settings WHERE setting_key = :key", ['key' => $key]);
    return $setting ? $setting['setting_value'] : $default;
}

/**
 * تحديث إعداد النظام
 */
function updateSetting($key, $value) {
    global $db;
    
    $exists = $db->exists('settings', 'setting_key = :key', ['key' => $key]);
    
    if ($exists) {
        return $db->update('settings', ['setting_value' => $value], 'setting_key = :key', ['key' => $key]);
    } else {
        return $db->insert('settings', ['setting_key' => $key, 'setting_value' => $value]);
    }
}

/**
 * تحويل الأرقام إلى كلمات (عربي)
 */
function numberToWords($number) {
    // تحويل بسيط للأرقام - يمكن تطويره أكثر
    $ones = ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة'];
    $tens = ['', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'];
    $hundreds = ['', 'مائة', 'مائتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'];
    
    if ($number == 0) return 'صفر';
    
    $result = '';
    
    // معالجة الآلاف
    if ($number >= 1000) {
        $thousands = intval($number / 1000);
        $result .= $ones[$thousands] . ' ألف ';
        $number %= 1000;
    }
    
    // معالجة المئات
    if ($number >= 100) {
        $hundredsDigit = intval($number / 100);
        $result .= $hundreds[$hundredsDigit] . ' ';
        $number %= 100;
    }
    
    // معالجة العشرات والآحاد
    if ($number >= 20) {
        $tensDigit = intval($number / 10);
        $result .= $tens[$tensDigit] . ' ';
        $number %= 10;
    } elseif ($number >= 10) {
        $teens = ['عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'];
        $result .= $teens[$number - 10] . ' ';
        $number = 0;
    }
    
    if ($number > 0) {
        $result .= $ones[$number] . ' ';
    }
    
    return trim($result);
}

/**
 * إنشاء pagination
 */
function createPagination($currentPage, $totalPages, $baseUrl, $params = []) {
    $pagination = '';
    
    if ($totalPages <= 1) return $pagination;
    
    $queryString = http_build_query($params);
    $separator = $queryString ? '&' : '';
    
    $pagination .= '<nav aria-label="Page navigation">';
    $pagination .= '<ul class="pagination justify-content-center">';
    
    // الصفحة السابقة
    if ($currentPage > 1) {
        $prevPage = $currentPage - 1;
        $pagination .= '<li class="page-item">';
        $pagination .= '<a class="page-link" href="' . $baseUrl . '?' . $queryString . $separator . 'page=' . $prevPage . '">السابق</a>';
        $pagination .= '</li>';
    }
    
    // أرقام الصفحات
    $start = max(1, $currentPage - 2);
    $end = min($totalPages, $currentPage + 2);
    
    for ($i = $start; $i <= $end; $i++) {
        $active = $i == $currentPage ? 'active' : '';
        $pagination .= '<li class="page-item ' . $active . '">';
        $pagination .= '<a class="page-link" href="' . $baseUrl . '?' . $queryString . $separator . 'page=' . $i . '">' . $i . '</a>';
        $pagination .= '</li>';
    }
    
    // الصفحة التالية
    if ($currentPage < $totalPages) {
        $nextPage = $currentPage + 1;
        $pagination .= '<li class="page-item">';
        $pagination .= '<a class="page-link" href="' . $baseUrl . '?' . $queryString . $separator . 'page=' . $nextPage . '">التالي</a>';
        $pagination .= '</li>';
    }
    
    $pagination .= '</ul>';
    $pagination .= '</nav>';
    
    return $pagination;
}
?>
