<?php
/**
 * صفحة معاملات المتبرع (QR Code)
 * جمعية قوافل الخير
 */

define('SYSTEM_ACCESS', true);
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';

$donorCode = sanitize($_GET['code'] ?? '');

if (empty($donorCode)) {
    die('كود المتبرع مطلوب');
}

// الحصول على بيانات المتبرع
$donor = $db->selectOne("SELECT * FROM donors WHERE donor_code = :code", ['code' => $donorCode]);

if (!$donor) {
    die('المتبرع غير موجود');
}

// الحصول على تبرعات المتبرع
$donations = $db->select("
    SELECT d.*, u.full_name as created_by_name
    FROM donations d
    LEFT JOIN users u ON d.created_by = u.id
    WHERE d.donor_id = :donor_id
    ORDER BY d.created_at DESC
", ['donor_id' => $donor['id']]);

// حساب الإحصائيات
$totalAmount = 0;
$totalDonations = count($donations);
$donationsByType = [];

foreach ($donations as $donation) {
    $totalAmount += $donation['amount'];
    
    if (!isset($donationsByType[$donation['donation_type']])) {
        $donationsByType[$donation['donation_type']] = [
            'count' => 0,
            'amount' => 0
        ];
    }
    
    $donationsByType[$donation['donation_type']]['count']++;
    $donationsByType[$donation['donation_type']]['amount'] += $donation['amount'];
}

$orgName = getSetting('organization_name', 'جمعية قوافل الخير');
$orgPhone = getSetting('organization_phone', '');
$orgEmail = getSetting('organization_email', '');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجل التبرعات - <?php echo htmlspecialchars($donor['name']); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-weight: 700;
            font-size: 2rem;
        }
        
        .header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }
        
        .donor-info {
            background: #f8f9fa;
            padding: 2rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        .donor-name {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c5aa0;
            margin-bottom: 1rem;
        }
        
        .stats-row {
            display: flex;
            justify-content: space-around;
            margin-top: 1rem;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #2c5aa0;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .content {
            padding: 2rem;
        }
        
        .section-title {
            background: #2c5aa0;
            color: white;
            padding: 1rem;
            margin: 0 0 1rem 0;
            font-weight: 600;
            border-radius: 5px;
        }
        
        .donation-card {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 1rem;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .donation-card:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        
        .donation-header {
            background: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .donation-body {
            padding: 1rem;
        }
        
        .donation-amount {
            font-size: 1.5rem;
            font-weight: 700;
            color: #28a745;
        }
        
        .donation-type {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .type-monthly_sponsorship {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .type-restricted {
            background: #fff3e0;
            color: #f57c00;
        }
        
        .type-unrestricted {
            background: #e8f5e8;
            color: #388e3c;
        }
        
        .download-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .download-btn:hover {
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
        }
        
        .footer {
            background: #2c5aa0;
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .contact-info {
            margin-top: 1rem;
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        @media (max-width: 768px) {
            .stats-row {
                flex-direction: column;
                gap: 1rem;
            }
            
            .donation-header {
                flex-direction: column;
                gap: 0.5rem;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header">
            <h1><?php echo htmlspecialchars($orgName); ?></h1>
            <p>سجل التبرعات الشخصي</p>
        </div>
        
        <!-- Donor Information -->
        <div class="donor-info">
            <div class="donor-name">
                <i class="fas fa-user-circle me-2"></i>
                <?php echo htmlspecialchars($donor['name']); ?>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <?php if ($donor['phone']): ?>
                        <p><i class="fas fa-phone me-2"></i> <?php echo htmlspecialchars($donor['phone']); ?></p>
                    <?php endif; ?>
                    <?php if ($donor['email']): ?>
                        <p><i class="fas fa-envelope me-2"></i> <?php echo htmlspecialchars($donor['email']); ?></p>
                    <?php endif; ?>
                </div>
                <div class="col-md-6">
                    <p><i class="fas fa-barcode me-2"></i> كود المتبرع: <?php echo $donor['donor_code']; ?></p>
                    <p><i class="fas fa-calendar me-2"></i> تاريخ التسجيل: <?php echo formatDate($donor['created_at']); ?></p>
                </div>
            </div>
            
            <div class="stats-row">
                <div class="stat-item">
                    <div class="stat-number"><?php echo number_format($totalDonations); ?></div>
                    <div class="stat-label">إجمالي التبرعات</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo formatAmount($totalAmount); ?></div>
                    <div class="stat-label">إجمالي المبلغ</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo count($donationsByType); ?></div>
                    <div class="stat-label">أنواع التبرعات</div>
                </div>
            </div>
        </div>
        
        <!-- Content -->
        <div class="content">
            <!-- Statistics by Type -->
            <?php if (!empty($donationsByType)): ?>
                <h3 class="section-title">
                    <i class="fas fa-chart-pie me-2"></i>
                    إحصائيات التبرعات حسب النوع
                </h3>
                
                <div class="row mb-4">
                    <?php foreach ($donationsByType as $type => $stats): ?>
                        <div class="col-md-4 mb-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5 class="card-title"><?php echo DONATION_TYPES[$type]; ?></h5>
                                    <p class="card-text">
                                        <strong><?php echo number_format($stats['count']); ?></strong> تبرع<br>
                                        <strong><?php echo formatAmount($stats['amount']); ?></strong>
                                    </p>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <!-- Donations List -->
            <h3 class="section-title">
                <i class="fas fa-list me-2"></i>
                سجل التبرعات التفصيلي
            </h3>
            
            <?php if (!empty($donations)): ?>
                <?php foreach ($donations as $donation): ?>
                    <div class="donation-card">
                        <div class="donation-header">
                            <div>
                                <span class="donation-type type-<?php echo $donation['donation_type']; ?>">
                                    <?php echo DONATION_TYPES[$donation['donation_type']]; ?>
                                </span>
                                <small class="text-muted ms-2">
                                    الرقم التسلسلي: <?php echo $donation['serial_number']; ?>
                                </small>
                            </div>
                            <div class="donation-amount">
                                <?php echo formatAmount($donation['amount'], $donation['currency']); ?>
                            </div>
                        </div>
                        
                        <div class="donation-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <p><strong>التاريخ:</strong> <?php echo formatDateTime($donation['created_at']); ?></p>
                                    <p><strong>طريقة الدفع:</strong> <?php echo PAYMENT_METHODS[$donation['payment_method']]; ?></p>
                                    
                                    <?php if ($donation['project_name']): ?>
                                        <p><strong>المشروع:</strong> <?php echo htmlspecialchars($donation['project_name']); ?></p>
                                    <?php endif; ?>
                                    
                                    <?php if ($donation['beneficiary_family']): ?>
                                        <p><strong>العائلة المستفيدة:</strong> <?php echo htmlspecialchars($donation['beneficiary_family']); ?></p>
                                    <?php endif; ?>
                                    
                                    <?php if ($donation['sponsorship_duration']): ?>
                                        <p><strong>مدة الكفالة:</strong> <?php echo $donation['sponsorship_duration']; ?> شهر</p>
                                    <?php endif; ?>
                                    
                                    <?php if ($donation['notes']): ?>
                                        <p><strong>ملاحظات:</strong> <?php echo htmlspecialchars($donation['notes']); ?></p>
                                    <?php endif; ?>
                                    
                                    <p><strong>المسؤول:</strong> <?php echo htmlspecialchars($donation['created_by_name']); ?></p>
                                </div>
                                <div class="col-md-4 text-end">
                                    <?php if ($donation['receipt_path'] && file_exists(RECEIPTS_PATH . '/' . $donation['receipt_path'])): ?>
                                        <a href="../uploads/receipts/<?php echo $donation['receipt_path']; ?>" 
                                           class="download-btn" target="_blank">
                                            <i class="fas fa-download me-1"></i>
                                            تحميل الإيصال
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد تبرعات بعد</h4>
                    <p class="text-muted">لم يتم تسجيل أي تبرعات لهذا المتبرع حتى الآن</p>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <h5>شكراً لكم على تبرعاتكم الكريمة</h5>
            <p>نسأل الله أن يتقبل منكم ويجعله في ميزان حسناتكم</p>
            
            <div class="contact-info">
                <?php if ($orgPhone): ?>
                    <p><i class="fas fa-phone me-2"></i> <?php echo htmlspecialchars($orgPhone); ?></p>
                <?php endif; ?>
                <?php if ($orgEmail): ?>
                    <p><i class="fas fa-envelope me-2"></i> <?php echo htmlspecialchars($orgEmail); ?></p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
