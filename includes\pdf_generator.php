<?php
/**
 * مولد ملفات PDF للإيصالات
 * جمعية قوافل الخير
 */

// منع الوصول المباشر
if (!defined('SYSTEM_ACCESS')) {
    die('Access denied');
}

require_once 'vendor/autoload.php';

use Dompdf\Dompdf;
use Dompdf\Options;
use chillerlan\QRCode\QRCode;
use chillerlan\QRCode\QROptions;

class PDFGenerator {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * توليد إيصال التبرع
     */
    public function generateDonationReceipt($donationId) {
        // الحصول على بيانات التبرع
        $donation = $this->db->selectOne("
            SELECT d.*, don.name as donor_name, don.phone as donor_phone,
                   don.email as donor_email, don.address as donor_address,
                   don.donor_code, u.full_name as created_by_name
            FROM donations d
            LEFT JOIN donors don ON d.donor_id = don.id
            LEFT JOIN users u ON d.created_by = u.id
            WHERE d.id = :id
        ", ['id' => $donationId]);
        
        if (!$donation) {
            return false;
        }
        
        // توليد QR Code
        $qrUrl = SYSTEM_URL . '/public/donor_transactions.php?code=' . $donation['donor_code'];
        $qrCode = $this->generateQRCode($qrUrl);
        
        // إنشاء HTML للإيصال
        $html = $this->createReceiptHTML($donation, $qrCode);
        
        // توليد PDF
        $filename = 'receipt_' . $donation['serial_number'] . '.pdf';
        $pdfPath = RECEIPTS_PATH . '/' . $filename;
        
        if ($this->generatePDF($html, $pdfPath)) {
            // تحديث مسار الإيصال في قاعدة البيانات
            $this->db->update('donations', 
                ['receipt_path' => $filename, 'qr_code' => $qrUrl], 
                'id = :id', 
                ['id' => $donationId]
            );
            
            return $filename;
        }
        
        return false;
    }
    
    /**
     * توليد QR Code
     */
    private function generateQRCode($url) {
        $options = new QROptions([
            'version'    => 5,
            'outputType' => QRCode::OUTPUT_IMAGE_PNG,
            'eccLevel'   => QRCode::ECC_L,
            'scale'      => 6,
            'imageBase64' => true,
        ]);
        
        $qrcode = new QRCode($options);
        return $qrcode->render($url);
    }
    
    /**
     * إنشاء HTML للإيصال
     */
    private function createReceiptHTML($donation, $qrCode) {
        $orgName = getSetting('organization_name', 'جمعية قوافل الخير');
        $orgAddress = getSetting('organization_address', '');
        $orgPhone = getSetting('organization_phone', '');
        $orgEmail = getSetting('organization_email', '');
        $receiptFooter = getSetting('receipt_footer', 'شكراً لكم على تبرعكم الكريم');
        
        $donationType = DONATION_TYPES[$donation['donation_type']];
        $paymentMethod = PAYMENT_METHODS[$donation['payment_method']];
        $amountInWords = numberToWords($donation['amount']);
        
        $html = '
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <style>
                @import url("https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap");
                
                body {
                    font-family: "Cairo", Arial, sans-serif;
                    margin: 0;
                    padding: 20px;
                    font-size: 14px;
                    line-height: 1.6;
                    color: #333;
                }
                
                .receipt-container {
                    max-width: 800px;
                    margin: 0 auto;
                    border: 2px solid #2c5aa0;
                    border-radius: 10px;
                    overflow: hidden;
                }
                
                .header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 30px;
                    text-align: center;
                    position: relative;
                }
                
                .header h1 {
                    margin: 0;
                    font-size: 28px;
                    font-weight: 700;
                }
                
                .header p {
                    margin: 5px 0;
                    opacity: 0.9;
                }
                
                .receipt-title {
                    background: #f8f9fa;
                    padding: 20px;
                    text-align: center;
                    border-bottom: 2px solid #e9ecef;
                }
                
                .receipt-title h2 {
                    margin: 0;
                    color: #2c5aa0;
                    font-size: 24px;
                    font-weight: 600;
                }
                
                .receipt-info {
                    display: flex;
                    justify-content: space-between;
                    padding: 20px;
                    background: #f8f9fa;
                    border-bottom: 1px solid #e9ecef;
                }
                
                .receipt-number {
                    text-align: right;
                }
                
                .receipt-date {
                    text-align: left;
                }
                
                .content {
                    padding: 30px;
                }
                
                .donor-info, .donation-info {
                    margin-bottom: 30px;
                }
                
                .section-title {
                    background: #2c5aa0;
                    color: white;
                    padding: 10px 15px;
                    margin: 0 0 15px 0;
                    font-weight: 600;
                    border-radius: 5px;
                }
                
                .info-row {
                    display: flex;
                    margin-bottom: 10px;
                    padding: 8px 0;
                    border-bottom: 1px dotted #ddd;
                }
                
                .info-label {
                    font-weight: 600;
                    width: 150px;
                    color: #555;
                }
                
                .info-value {
                    flex: 1;
                    color: #333;
                }
                
                .amount-section {
                    background: #e8f4fd;
                    border: 2px solid #2c5aa0;
                    border-radius: 10px;
                    padding: 20px;
                    margin: 20px 0;
                    text-align: center;
                }
                
                .amount-number {
                    font-size: 36px;
                    font-weight: 700;
                    color: #2c5aa0;
                    margin-bottom: 10px;
                }
                
                .amount-words {
                    font-size: 16px;
                    color: #666;
                    font-style: italic;
                }
                
                .qr-section {
                    text-align: center;
                    padding: 20px;
                    border-top: 2px solid #e9ecef;
                }
                
                .qr-code {
                    margin: 10px 0;
                }
                
                .footer {
                    background: #2c5aa0;
                    color: white;
                    padding: 20px;
                    text-align: center;
                }
                
                .signature-section {
                    display: flex;
                    justify-content: space-between;
                    margin-top: 40px;
                    padding-top: 20px;
                    border-top: 1px solid #ddd;
                }
                
                .signature-box {
                    text-align: center;
                    width: 200px;
                }
                
                .signature-line {
                    border-top: 1px solid #333;
                    margin-top: 40px;
                    padding-top: 5px;
                }
                
                @media print {
                    body { margin: 0; padding: 0; }
                    .receipt-container { border: none; }
                }
            </style>
        </head>
        <body>
            <div class="receipt-container">
                <!-- Header -->
                <div class="header">
                    <h1>' . htmlspecialchars($orgName) . '</h1>
                    <p>' . htmlspecialchars($orgAddress) . '</p>
                    <p>هاتف: ' . htmlspecialchars($orgPhone) . ' | بريد إلكتروني: ' . htmlspecialchars($orgEmail) . '</p>
                </div>
                
                <!-- Receipt Title -->
                <div class="receipt-title">
                    <h2>إيصال تبرع</h2>
                </div>
                
                <!-- Receipt Info -->
                <div class="receipt-info">
                    <div class="receipt-number">
                        <strong>رقم الإيصال:</strong> _______________<br>
                        <strong>الرقم التسلسلي:</strong> ' . $donation['serial_number'] . '
                    </div>
                    <div class="receipt-date">
                        <strong>التاريخ:</strong> ' . formatDate($donation['created_at']) . '<br>
                        <strong>الوقت:</strong> ' . date('H:i', strtotime($donation['created_at'])) . '
                    </div>
                </div>
                
                <!-- Content -->
                <div class="content">
                    <!-- Donor Information -->
                    <div class="donor-info">
                        <h3 class="section-title">بيانات المتبرع</h3>
                        <div class="info-row">
                            <div class="info-label">الاسم:</div>
                            <div class="info-value">' . htmlspecialchars($donation['donor_name']) . '</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">رقم الهاتف:</div>
                            <div class="info-value">' . htmlspecialchars($donation['donor_phone'] ?: 'غير محدد') . '</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">البريد الإلكتروني:</div>
                            <div class="info-value">' . htmlspecialchars($donation['donor_email'] ?: 'غير محدد') . '</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">العنوان:</div>
                            <div class="info-value">' . htmlspecialchars($donation['donor_address'] ?: 'غير محدد') . '</div>
                        </div>
                    </div>
                    
                    <!-- Donation Information -->
                    <div class="donation-info">
                        <h3 class="section-title">بيانات التبرع</h3>
                        <div class="info-row">
                            <div class="info-label">نوع التبرع:</div>
                            <div class="info-value">' . $donationType . '</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">طريقة الدفع:</div>
                            <div class="info-value">' . $paymentMethod . '</div>
                        </div>';
        
        if ($donation['project_name']) {
            $html .= '
                        <div class="info-row">
                            <div class="info-label">اسم المشروع:</div>
                            <div class="info-value">' . htmlspecialchars($donation['project_name']) . '</div>
                        </div>';
        }
        
        if ($donation['beneficiary_family']) {
            $html .= '
                        <div class="info-row">
                            <div class="info-label">العائلة المستفيدة:</div>
                            <div class="info-value">' . htmlspecialchars($donation['beneficiary_family']) . '</div>
                        </div>';
        }
        
        if ($donation['sponsorship_duration']) {
            $html .= '
                        <div class="info-row">
                            <div class="info-label">مدة الكفالة:</div>
                            <div class="info-value">' . $donation['sponsorship_duration'] . ' شهر</div>
                        </div>';
        }
        
        if ($donation['notes']) {
            $html .= '
                        <div class="info-row">
                            <div class="info-label">ملاحظات:</div>
                            <div class="info-value">' . htmlspecialchars($donation['notes']) . '</div>
                        </div>';
        }
        
        $html .= '
                    </div>
                    
                    <!-- Amount Section -->
                    <div class="amount-section">
                        <div class="amount-number">' . formatAmount($donation['amount'], $donation['currency']) . '</div>
                        <div class="amount-words">(' . $amountInWords . ' ' . $donation['currency'] . ' فقط لا غير)</div>
                    </div>
                    
                    <!-- Signature Section -->
                    <div class="signature-section">
                        <div class="signature-box">
                            <div>توقيع المتبرع</div>
                            <div class="signature-line">_______________</div>
                        </div>
                        <div class="signature-box">
                            <div>توقيع المسؤول</div>
                            <div class="signature-line">' . htmlspecialchars($donation['created_by_name']) . '</div>
                        </div>
                    </div>
                </div>
                
                <!-- QR Code Section -->
                <div class="qr-section">
                    <p><strong>امسح الكود للاطلاع على سجل تبرعاتك</strong></p>
                    <div class="qr-code">
                        <img src="' . $qrCode . '" alt="QR Code" style="width: 120px; height: 120px;">
                    </div>
                    <small>كود المتبرع: ' . $donation['donor_code'] . '</small>
                </div>
                
                <!-- Footer -->
                <div class="footer">
                    <p>' . htmlspecialchars($receiptFooter) . '</p>
                    <p>تم إنشاء هذا الإيصال بواسطة نظام إدارة التبرعات</p>
                </div>
            </div>
        </body>
        </html>';
        
        return $html;
    }
    
    /**
     * توليد ملف PDF
     */
    private function generatePDF($html, $outputPath) {
        try {
            $options = new Options();
            $options->set('defaultFont', 'DejaVu Sans');
            $options->set('isRemoteEnabled', true);
            $options->set('isHtml5ParserEnabled', true);
            
            $dompdf = new Dompdf($options);
            $dompdf->loadHtml($html);
            $dompdf->setPaper('A4', 'portrait');
            $dompdf->render();
            
            $output = $dompdf->output();
            file_put_contents($outputPath, $output);
            
            return true;
        } catch (Exception $e) {
            error_log("PDF Generation Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * توليد تقرير PDF
     */
    public function generateReport($title, $data, $columns, $filename = null) {
        if (!$filename) {
            $filename = 'report_' . date('Y-m-d_H-i-s') . '.pdf';
        }
        
        $html = $this->createReportHTML($title, $data, $columns);
        $pdfPath = UPLOADS_PATH . '/' . $filename;
        
        if ($this->generatePDF($html, $pdfPath)) {
            return $filename;
        }
        
        return false;
    }
    
    /**
     * إنشاء HTML للتقرير
     */
    private function createReportHTML($title, $data, $columns) {
        $orgName = getSetting('organization_name', 'جمعية قوافل الخير');
        
        $html = '
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <style>
                body {
                    font-family: "DejaVu Sans", Arial, sans-serif;
                    margin: 20px;
                    font-size: 12px;
                }
                
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                    border-bottom: 2px solid #333;
                    padding-bottom: 20px;
                }
                
                .header h1 {
                    margin: 0;
                    color: #2c5aa0;
                }
                
                .header h2 {
                    margin: 10px 0;
                    color: #666;
                }
                
                .report-info {
                    margin-bottom: 20px;
                    text-align: left;
                }
                
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                
                th, td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: right;
                }
                
                th {
                    background-color: #f2f2f2;
                    font-weight: bold;
                }
                
                .footer {
                    margin-top: 30px;
                    text-align: center;
                    font-size: 10px;
                    color: #666;
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>' . htmlspecialchars($orgName) . '</h1>
                <h2>' . htmlspecialchars($title) . '</h2>
            </div>
            
            <div class="report-info">
                <p><strong>تاريخ التقرير:</strong> ' . formatDate(date('Y-m-d')) . '</p>
                <p><strong>عدد السجلات:</strong> ' . count($data) . '</p>
            </div>
            
            <table>
                <thead>
                    <tr>';
        
        foreach ($columns as $column) {
            $html .= '<th>' . htmlspecialchars($column) . '</th>';
        }
        
        $html .= '
                    </tr>
                </thead>
                <tbody>';
        
        foreach ($data as $row) {
            $html .= '<tr>';
            foreach ($row as $cell) {
                $html .= '<td>' . htmlspecialchars($cell) . '</td>';
            }
            $html .= '</tr>';
        }
        
        $html .= '
                </tbody>
            </table>
            
            <div class="footer">
                <p>تم إنشاء هذا التقرير بواسطة نظام إدارة التبرعات - ' . htmlspecialchars($orgName) . '</p>
            </div>
        </body>
        </html>';
        
        return $html;
    }
}
?>
