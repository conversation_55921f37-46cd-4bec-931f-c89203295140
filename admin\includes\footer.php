        </div>
    </div>
    
    <!-- Footer -->
    <footer class="sticky-footer bg-white">
        <div class="container my-auto">
            <div class="copyright text-center my-auto">
                <span>حقوق الطبع والنشر &copy; جمعية قوافل الخير <?php echo date('Y'); ?></span>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <script>
        // Sidebar toggle for mobile
        document.getElementById('sidebarToggle')?.addEventListener('click', function() {
            document.querySelector('.sidebar').classList.toggle('show');
        });
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
        
        // Initialize DataTables with Arabic language
        $(document).ready(function() {
            $('.data-table').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json"
                },
                "order": [[ 0, "desc" ]],
                "pageLength": 25,
                "responsive": true
            });
        });
        
        // Confirmation dialogs
        function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
            return confirm(message);
        }
        
        // Format numbers
        function formatNumber(num) {
            return new Intl.NumberFormat('ar-SA').format(num);
        }
        
        // Format currency
        function formatCurrency(amount, currency = 'SAR') {
            return new Intl.NumberFormat('ar-SA', {
                style: 'currency',
                currency: currency
            }).format(amount);
        }
        
        // Print function
        function printPage() {
            window.print();
        }
        
        // Export to Excel (simple CSV)
        function exportToCSV(tableId, filename = 'export.csv') {
            const table = document.getElementById(tableId);
            if (!table) return;
            
            let csv = [];
            const rows = table.querySelectorAll('tr');
            
            for (let i = 0; i < rows.length; i++) {
                const row = [];
                const cols = rows[i].querySelectorAll('td, th');
                
                for (let j = 0; j < cols.length; j++) {
                    row.push(cols[j].innerText);
                }
                
                csv.push(row.join(','));
            }
            
            const csvContent = csv.join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            
            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }
        
        // Form validation
        function validateForm(formId) {
            const form = document.getElementById(formId);
            if (!form) return false;
            
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(function(field) {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });
            
            return isValid;
        }
        
        // Real-time form validation
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form');
            
            forms.forEach(function(form) {
                const fields = form.querySelectorAll('input, select, textarea');
                
                fields.forEach(function(field) {
                    field.addEventListener('blur', function() {
                        if (this.hasAttribute('required') && !this.value.trim()) {
                            this.classList.add('is-invalid');
                        } else {
                            this.classList.remove('is-invalid');
                        }
                    });
                    
                    field.addEventListener('input', function() {
                        if (this.classList.contains('is-invalid') && this.value.trim()) {
                            this.classList.remove('is-invalid');
                        }
                    });
                });
            });
        });
        
        // Auto-save draft functionality
        function autoSaveDraft(formId, interval = 30000) {
            const form = document.getElementById(formId);
            if (!form) return;
            
            setInterval(function() {
                const formData = new FormData(form);
                const data = Object.fromEntries(formData);
                
                localStorage.setItem('draft_' + formId, JSON.stringify(data));
            }, interval);
        }
        
        // Load draft
        function loadDraft(formId) {
            const draft = localStorage.getItem('draft_' + formId);
            if (!draft) return;
            
            const data = JSON.parse(draft);
            const form = document.getElementById(formId);
            
            Object.keys(data).forEach(function(key) {
                const field = form.querySelector('[name="' + key + '"]');
                if (field) {
                    field.value = data[key];
                }
            });
        }
        
        // Clear draft
        function clearDraft(formId) {
            localStorage.removeItem('draft_' + formId);
        }
        
        // Copy to clipboard
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('تم النسخ إلى الحافظة');
            });
        }
        
        // Show loading spinner
        function showLoading(element) {
            if (typeof element === 'string') {
                element = document.getElementById(element);
            }
            
            if (element) {
                element.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
                element.disabled = true;
            }
        }
        
        // Hide loading spinner
        function hideLoading(element, originalText) {
            if (typeof element === 'string') {
                element = document.getElementById(element);
            }
            
            if (element) {
                element.innerHTML = originalText;
                element.disabled = false;
            }
        }
    </script>
    
    <?php if (isset($additionalJS)): ?>
        <?php echo $additionalJS; ?>
    <?php endif; ?>
</body>
</html>
