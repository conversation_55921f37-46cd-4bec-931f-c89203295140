<?php
/**
 * ملف الإعدادات الأساسي لنظام إدارة التبرعات
 * جمعية قوافل الخير
 * تم إنشاؤه تلقائياً في: 2025-06-15 02:41:50
 */

// منع الوصول المباشر
if (!defined('SYSTEM_ACCESS')) {
    die('Access denied');
}

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'qawafil_donations');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات النظام
define('SYSTEM_NAME', 'نظام إدارة التبرعات - جمعية قوافل الخير');
define('SYSTEM_VERSION', '1.0.0');
define('SYSTEM_URL', 'http://localhost/mosra-kher');
define('ADMIN_URL', SYSTEM_URL . '/admin');
define('PUBLIC_URL', SYSTEM_URL . '/public');

// مسارات الملفات
define('ROOT_PATH', dirname(__DIR__));
define('INCLUDES_PATH', ROOT_PATH . '/includes');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');
define('RECEIPTS_PATH', UPLOADS_PATH . '/receipts');
define('BACKUPS_PATH', UPLOADS_PATH . '/backups');

// إعدادات الجلسة
define('SESSION_NAME', 'qawafil_session');
define('SESSION_LIFETIME', 3600);

// إعدادات الأمان
define('HASH_ALGO', 'sha256');
define('ENCRYPTION_KEY', 'qawafil_donations_2024_secret_key');

// إعدادات التحميل
define('MAX_FILE_SIZE', 5 * 1024 * 1024);
define('ALLOWED_EXTENSIONS', ['pdf', 'jpg', 'jpeg', 'png']);

// إعدادات PDF
define('PDF_FONT', 'DejaVuSans');
define('PDF_FONT_SIZE', 12);

// إعدادات التاريخ والوقت
date_default_timezone_set('Asia/Riyadh');
define('DATE_FORMAT', 'Y-m-d');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');
define('DISPLAY_DATE_FORMAT', 'd/m/Y');
define('DISPLAY_DATETIME_FORMAT', 'd/m/Y H:i');

// إعدادات اللغة
define('SYSTEM_LANG', 'ar');
define('SYSTEM_DIRECTION', 'rtl');

// إعدادات التصحيح
define('DEBUG_MODE', false);
define('LOG_ERRORS', true);
define('ERROR_LOG_FILE', ROOT_PATH . '/logs/error.log');

// أنواع التبرعات
define('DONATION_TYPES', [
    'monthly_sponsorship' => 'الكفالات الشهرية',
    'restricted' => 'التبرعات المقيدة',
    'unrestricted' => 'التبرعات غير المقيدة'
]);

// طرق الدفع
define('PAYMENT_METHODS', [
    'cash' => 'نقدي',
    'bank_account' => 'عبر الحساب'
]);

// أنواع الجنس
define('GENDER_TYPES', [
    'male' => 'ذكر',
    'female' => 'أنثى'
]);

// أنواع التقييد
define('RESTRICTION_TYPES', [
    'family' => 'لعائلة',
    'project' => 'لمشروع'
]);

// المشاريع المتاحة
define('PROJECT_TYPES', [
    'HOUSE_BUY' => 'شراء البيوت',
    'HOUSE_REPAIR' => 'ترميم بيوت الأيتام',
    'HOUSE_FURNISH' => 'تأثيث بيوت الأيتام',
    'RENT_PAY' => 'تسديد حقوق الكراء',
    'RELIEF' => 'تفريج الكربات',
    'HEALTH' => 'الحالات الصحية',
    'ENTERTAINMENT' => 'الترفيه',
    'ORPHAN_CARE' => 'جبر خواطر الأيتام',
    'RAMADAN_BASKET' => 'قفة رمضان',
    'ORPHAN_CLOTHES' => 'كسوة الأيتام',
    'EID_ADHA' => 'عيد الأضحى',
    'SCHOOL_ENTRY' => 'الدخول المدرسي',
    'OUTSTANDING_ORPHAN' => 'اليتيم المتميز',
    'PRODUCTIVE_WIDOW' => 'الأرملة المنتجة',
    'IDEAL_WIDOW' => 'الأرملة المثالية'
]);

// الولايات الجزائرية
define('ALGERIAN_STATES', [
    '01' => 'أدرار', '02' => 'الشلف', '03' => 'الأغواط', '04' => 'أم البواقي',
    '05' => 'باتنة', '06' => 'بجاية', '07' => 'بسكرة', '08' => 'بشار',
    '09' => 'البليدة', '10' => 'البويرة', '11' => 'تمنراست', '12' => 'تبسة',
    '13' => 'تلمسان', '14' => 'تيارت', '15' => 'تيزي وزو', '16' => 'الجزائر',
    '17' => 'الجلفة', '18' => 'جيجل', '19' => 'سطيف', '20' => 'سعيدة',
    '21' => 'سكيكدة', '22' => 'سيدي بلعباس', '23' => 'عنابة', '24' => 'قالمة',
    '25' => 'قسنطينة', '26' => 'المدية', '27' => 'مستغانم', '28' => 'المسيلة',
    '29' => 'معسكر', '30' => 'ورقلة', '31' => 'وهران', '32' => 'البيض',
    '33' => 'إليزي', '34' => 'برج بوعريريج', '35' => 'بومرداس', '36' => 'الطارف',
    '37' => 'تندوف', '38' => 'تيسمسيلت', '39' => 'الوادي', '40' => 'خنشلة',
    '41' => 'سوق أهراس', '42' => 'تيبازة', '43' => 'ميلة', '44' => 'عين الدفلى',
    '45' => 'النعامة', '46' => 'عين تموشنت', '47' => 'غرداية', '48' => 'غليزان',
    '49' => 'تيميمون', '50' => 'برج باجي مختار', '51' => 'أولاد جلال', '52' => 'بني عباس',
    '53' => 'عين صالح', '54' => 'عين قزام', '55' => 'تقرت', '56' => 'جانت',
    '57' => 'المغير', '58' => 'المنيعة'
]);

// أنواع التقييد
define('RESTRICTION_TYPES', [
    'family' => 'لعائلة',
    'project' => 'لمشروع'
]);

// أنواع المشاريع
define('PROJECT_TYPES', [
    'buy_houses' => 'شراء البيوت',
    'renovate_orphan_houses' => 'ترميم بيوت الأيتام',
    'furnish_orphan_houses' => 'تأثيث بيوت الأيتام',
    'pay_rent' => 'تسديد حقوق الكراء',
    'relieve_distress' => 'تفريج الكربات',
    'health_cases' => 'الحالات الصحية',
    'entertainment' => 'الترفيه',
    'comfort_orphans' => 'جبر خواطر الأيتام',
    'ramadan_basket' => 'قفة رمضان',
    'orphan_clothing' => 'كسوة الأيتام',
    'eid_sacrifice' => 'عيد الأضحى',
    'school_entry' => 'الدخول المدرسي',
    'distinguished_orphan' => 'اليتيم المتميز',
    'productive_widow' => 'الأرملة المنتجة',
    'exemplary_widow' => 'الأرملة المثالية'
]);

// الولايات الجزائرية
define('ALGERIAN_STATES', [
    'adrar' => 'أدرار',
    'chlef' => 'الشلف',
    'laghouat' => 'الأغواط',
    'oum_bouaghi' => 'أم البواقي',
    'batna' => 'باتنة',
    'bejaia' => 'بجاية',
    'biskra' => 'بسكرة',
    'bechar' => 'بشار',
    'blida' => 'البليدة',
    'bouira' => 'البويرة',
    'tamanrasset' => 'تمنراست',
    'tebessa' => 'تبسة',
    'tlemcen' => 'تلمسان',
    'tiaret' => 'تيارت',
    'tizi_ouzou' => 'تيزي وزو',
    'algiers' => 'الجزائر',
    'djelfa' => 'الجلفة',
    'jijel' => 'جيجل',
    'setif' => 'سطيف',
    'saida' => 'سعيدة',
    'skikda' => 'سكيكدة',
    'sidi_bel_abbes' => 'سيدي بلعباس',
    'annaba' => 'عنابة',
    'guelma' => 'قالمة',
    'constantine' => 'قسنطينة',
    'medea' => 'المدية',
    'mostaganem' => 'مستغانم',
    'msila' => 'المسيلة',
    'mascara' => 'معسكر',
    'ouargla' => 'ورقلة',
    'oran' => 'وهران',
    'el_bayadh' => 'البيض',
    'illizi' => 'إليزي',
    'bordj_bou_arreridj' => 'برج بوعريريج',
    'boumerdes' => 'بومرداس',
    'el_tarf' => 'الطارف',
    'tindouf' => 'تندوف',
    'tissemsilt' => 'تيسمسيلت',
    'el_oued' => 'الوادي',
    'khenchela' => 'خنشلة',
    'souk_ahras' => 'سوق أهراس',
    'tipaza' => 'تيبازة',
    'mila' => 'ميلة',
    'ain_defla' => 'عين الدفلى',
    'naama' => 'النعامة',
    'ain_temouchent' => 'عين تموشنت',
    'ghardaia' => 'غرداية',
    'relizane' => 'غليزان'
]);

// مستويات الصلاحيات
define('USER_ROLES', [
    'admin' => 'مدير النظام',
    'employee' => 'موظف',
    'user' => 'مستخدم'
]);

// الصلاحيات المتاحة
define('PERMISSIONS', [
    'users' => 'إدارة المستخدمين',
    'donations' => 'إدارة التبرعات',
    'reports' => 'التقارير والإحصائيات',
    'backup' => 'النسخ الاحتياطي',
    'settings' => 'إعدادات النظام'
]);

// إنشاء المجلدات المطلوبة إذا لم تكن موجودة
$required_dirs = [
    UPLOADS_PATH,
    RECEIPTS_PATH,
    BACKUPS_PATH,
    dirname(ERROR_LOG_FILE)
];

foreach ($required_dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}
?>
// علامة التثبيت المكتمل
define('SYSTEM_INSTALLED', true);
