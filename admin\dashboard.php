<?php
/**
 * لوحة التحكم الرئيسية
 * جمعية قوافل الخير
 */

define('SYSTEM_ACCESS', true);
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// التحقق من تسجيل الدخول
requireLogin();

// الحصول على الإحصائيات
$stats = [];

// إجمالي التبرعات
$totalDonations = $db->selectOne("SELECT COUNT(*) as count, SUM(amount) as total FROM donations");
$stats['total_donations'] = $totalDonations['count'] ?? 0;
$stats['total_amount'] = $totalDonations['total'] ?? 0;

// التبرعات هذا الشهر
$thisMonth = $db->selectOne("
    SELECT COUNT(*) as count, SUM(amount) as total 
    FROM donations 
    WHERE MONTH(created_at) = MONTH(CURRENT_DATE()) 
    AND YEAR(created_at) = YEAR(CURRENT_DATE())
");
$stats['month_donations'] = $thisMonth['count'] ?? 0;
$stats['month_amount'] = $thisMonth['total'] ?? 0;

// عدد المتبرعين
$stats['total_donors'] = $db->count('donors');

// عدد المستخدمين
$stats['total_users'] = $db->count('users');

// التبرعات حسب النوع
$donationTypes = $db->select("
    SELECT donation_type, COUNT(*) as count, SUM(amount) as total
    FROM donations 
    GROUP BY donation_type
");

// آخر التبرعات
$recentDonations = $db->select("
    SELECT d.*, don.name as donor_name
    FROM donations d
    LEFT JOIN donors don ON d.donor_id = don.id
    ORDER BY d.created_at DESC
    LIMIT 10
");

// آخر الأنشطة
$recentActivities = $db->select("
    SELECT al.*, u.full_name as user_name
    FROM activity_log al
    LEFT JOIN users u ON al.user_id = u.id
    ORDER BY al.created_at DESC
    LIMIT 10
");

$pageTitle = 'لوحة التحكم';
include 'includes/header.php';
?>

<div class="container-fluid">
    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي التبرعات
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['total_donations']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-donate fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                إجمالي المبلغ
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo formatAmount($stats['total_amount']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                تبرعات هذا الشهر
                            </div>
                            <div class="row no-gutters align-items-center">
                                <div class="col-auto">
                                    <div class="h5 mb-0 mr-3 font-weight-bold text-gray-800">
                                        <?php echo number_format($stats['month_donations']); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                عدد المتبرعين
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['total_donors']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- التبرعات حسب النوع -->
        <div class="col-xl-6 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">التبرعات حسب النوع</h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($donationTypes)): ?>
                        <?php foreach ($donationTypes as $type): ?>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span><?php echo DONATION_TYPES[$type['donation_type']]; ?></span>
                                    <span><?php echo number_format($type['count']); ?> تبرع</span>
                                </div>
                                <div class="progress">
                                    <?php 
                                    $percentage = $stats['total_donations'] > 0 ? 
                                        ($type['count'] / $stats['total_donations']) * 100 : 0;
                                    ?>
                                    <div class="progress-bar" role="progressbar" 
                                         style="width: <?php echo $percentage; ?>%"
                                         aria-valuenow="<?php echo $percentage; ?>" 
                                         aria-valuemin="0" aria-valuemax="100">
                                    </div>
                                </div>
                                <small class="text-muted">
                                    <?php echo formatAmount($type['total']); ?>
                                </small>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p class="text-muted text-center">لا توجد تبرعات بعد</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- آخر التبرعات -->
        <div class="col-xl-6 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">آخر التبرعات</h6>
                    <a href="donations.php" class="btn btn-sm btn-primary">عرض الكل</a>
                </div>
                <div class="card-body">
                    <?php if (!empty($recentDonations)): ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($recentDonations as $donation): ?>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1"><?php echo htmlspecialchars($donation['donor_name']); ?></h6>
                                        <p class="mb-1"><?php echo DONATION_TYPES[$donation['donation_type']]; ?></p>
                                        <small><?php echo formatDateTime($donation['created_at']); ?></small>
                                    </div>
                                    <span class="badge badge-primary badge-pill">
                                        <?php echo formatAmount($donation['amount']); ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p class="text-muted text-center">لا توجد تبرعات بعد</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- آخر الأنشطة -->
    <?php if ($auth->hasPermission('users')): ?>
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">آخر الأنشطة</h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($recentActivities)): ?>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>المستخدم</th>
                                        <th>العملية</th>
                                        <th>الجدول</th>
                                        <th>التاريخ</th>
                                        <th>عنوان IP</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recentActivities as $activity): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($activity['user_name']); ?></td>
                                            <td><?php echo htmlspecialchars($activity['action']); ?></td>
                                            <td><?php echo htmlspecialchars($activity['table_name']); ?></td>
                                            <td><?php echo formatDateTime($activity['created_at']); ?></td>
                                            <td><?php echo htmlspecialchars($activity['ip_address']); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted text-center">لا توجد أنشطة بعد</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php include 'includes/footer.php'; ?>
