# دليل تنصيب نظام إدارة التبرعات - جمعية قوافل الخير

## المتطلبات

### متطلبات الخادم
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث (أو MariaDB 10.2+)
- Apache أو Nginx
- مساحة تخزين: 100 ميجابايت على الأقل

### امتدادات PHP المطلوبة
- PDO
- PDO_MySQL
- GD
- mbstring
- zip
- curl
- json

### أدوات إضافية
- Composer (لإدارة المكتبات)
- Git (اختياري)

## خطوات التنصيب

### 1. تحميل الملفات

```bash
# استنساخ المشروع (إذا كان متاحاً على Git)
git clone [repository-url] donation-system
cd donation-system

# أو تحميل الملفات وفك الضغط
# ثم نقلها إلى مجلد الويب
```

### 2. تنصي<PERSON> المكتبات

```bash
# تنصيب Composer إذا لم يكن مثبتاً
curl -sS https://getcomposer.org/installer | php
mv composer.phar /usr/local/bin/composer

# تنصيب المكتبات المطلوبة
composer install
```

### 3. إعداد قاعدة البيانات

#### إنشاء قاعدة البيانات
```sql
CREATE DATABASE qawafil_donations CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### إنشاء مستخدم قاعدة البيانات (اختياري)
```sql
CREATE USER 'qawafil_user'@'localhost' IDENTIFIED BY 'strong_password_here';
GRANT ALL PRIVILEGES ON qawafil_donations.* TO 'qawafil_user'@'localhost';
FLUSH PRIVILEGES;
```

#### تشغيل ملف إعداد قاعدة البيانات
```bash
mysql -u root -p qawafil_donations < database/setup.sql
```

### 4. تكوين النظام

#### تعديل ملف الإعدادات
```php
// في ملف includes/config.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'qawafil_donations');
define('DB_USER', 'qawafil_user');  // أو root
define('DB_PASS', 'your_password_here');

// تحديث رابط النظام
define('SYSTEM_URL', 'http://your-domain.com/donation-system');
```

### 5. إعداد صلاحيات المجلدات

```bash
# إعطاء صلاحيات الكتابة للمجلدات المطلوبة
chmod 755 uploads/
chmod 755 uploads/receipts/
chmod 755 uploads/backups/
chmod 755 logs/

# في بعض الحالات قد تحتاج إلى
chown -R www-data:www-data uploads/
chown -R www-data:www-data logs/
```

### 6. إعداد الخادم

#### Apache (.htaccess)
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# حماية المجلدات الحساسة
<Files "*.php">
    Order Deny,Allow
    Deny from all
</Files>

<Files "index.php">
    Order Allow,Deny
    Allow from all
</Files>
```

#### Nginx
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/donation-system;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # حماية المجلدات الحساسة
    location ~ ^/(includes|database|vendor)/ {
        deny all;
    }
}
```

## بيانات الدخول الافتراضية

### المدير الرئيسي
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

### موظف التبرعات
- **اسم المستخدم:** employee1
- **كلمة المرور:** admin123

**⚠️ مهم:** يجب تغيير كلمات المرور فور التنصيب!

## التحقق من التنصيب

### 1. اختبار الاتصال بقاعدة البيانات
```bash
# زيارة الرابط التالي للتحقق
http://your-domain.com/donation-system/admin/login.php
```

### 2. اختبار الوظائف الأساسية
- تسجيل الدخول
- إضافة متبرع جديد
- إضافة تبرع
- توليد إيصال PDF
- إنشاء نسخة احتياطية

## الإعدادات الإضافية

### 1. إعداد البريد الإلكتروني (اختياري)
```php
// في ملف includes/config.php
define('MAIL_HOST', 'smtp.gmail.com');
define('MAIL_PORT', 587);
define('MAIL_USERNAME', '<EMAIL>');
define('MAIL_PASSWORD', 'your-app-password');
```

### 2. إعداد النسخ الاحتياطي التلقائي
```bash
# إضافة مهمة cron للنسخ الاحتياطي اليومي
0 2 * * * /usr/bin/php /path/to/donation-system/scripts/auto_backup.php
```

### 3. إعداد SSL (مستحسن)
```bash
# تنصيب Let's Encrypt
sudo apt install certbot python3-certbot-apache
sudo certbot --apache -d your-domain.com
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### خطأ في الاتصال بقاعدة البيانات
```
- تحقق من بيانات الاتصال في config.php
- تأكد من تشغيل خدمة MySQL
- تحقق من صلاحيات المستخدم
```

#### مشاكل في رفع الملفات
```
- تحقق من صلاحيات مجلد uploads/
- تحقق من إعدادات PHP (upload_max_filesize)
- تأكد من وجود مساحة كافية على القرص
```

#### مشاكل في توليد PDF
```
- تأكد من تنصيب مكتبة dompdf
- تحقق من صلاحيات مجلد receipts/
- تأكد من تفعيل امتداد GD في PHP
```

### ملفات السجلات
```
- أخطاء النظام: logs/error.log
- أخطاء Apache: /var/log/apache2/error.log
- أخطاء PHP: /var/log/php_errors.log
```

## الأمان

### إرشادات الأمان المهمة

1. **تغيير كلمات المرور الافتراضية**
2. **تحديث النظام بانتظام**
3. **إعداد نسخ احتياطية دورية**
4. **استخدام HTTPS**
5. **تقييد الوصول لمجلد الإدارة**
6. **مراقبة ملفات السجلات**

### حماية إضافية
```apache
# في ملف .htaccess لمجلد admin/
AuthType Basic
AuthName "Restricted Area"
AuthUserFile /path/to/.htpasswd
Require valid-user
```

## الدعم والصيانة

### النسخ الاحتياطية
- يُنصح بأخذ نسخة احتياطية يومية
- حفظ النسخ في مكان آمن خارج الخادم
- اختبار استعادة النسخ بانتظام

### التحديثات
- متابعة التحديثات الأمنية
- اختبار التحديثات في بيئة تجريبية أولاً
- أخذ نسخة احتياطية قبل أي تحديث

### المراقبة
- مراقبة استخدام المساحة
- مراقبة أداء قاعدة البيانات
- مراقبة ملفات السجلات للأخطاء

## الاتصال والدعم

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:

- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +966 11 123 4567

---

**ملاحظة:** هذا النظام تم تطويره خصيصاً لجمعية قوافل الخير. يُرجى قراءة هذا الدليل بعناية قبل البدء في التنصيب.
