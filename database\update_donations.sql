-- تحديث جدول التبرعات ليتوافق مع المتطلبات الجديدة

-- حذف الجدول القديم وإنشاء جديد
DROP TABLE IF EXISTS `donations`;

CREATE TABLE `donations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `donor_id` int(11) NOT NULL,
  `donation_type` enum('monthly_sponsorship','restricted','unrestricted') NOT NULL,
  
  -- حقول مشتركة
  `amount` decimal(10,2) NOT NULL,
  `currency` varchar(3) NOT NULL DEFAULT 'DZD',
  `payment_method` enum('cash','bank_transfer') NOT NULL DEFAULT 'cash',
  `registration_number` varchar(50) DEFAULT NULL,
  `withdrawal_number` varchar(50) DEFAULT NULL,
  `date` date NOT NULL,
  
  -- حقول الكفالات الشهرية
  `sponsor_name` varchar(255) DEFAULT NULL,
  `sponsor_gender` enum('male','female') DEFAULT NULL,
  `sponsor_phone` varchar(20) DEFAULT NULL,
  `sponsor_state` varchar(100) DEFAULT NULL,
  `monthly_amount` decimal(10,2) DEFAULT NULL,
  `family_number` varchar(50) DEFAULT NULL,
  `month` int(2) DEFAULT NULL,
  `year` int(4) DEFAULT NULL,
  `disbursement_voucher` varchar(50) DEFAULT NULL,
  
  -- حقول التبرعات المقيدة وغير المقيدة
  `donor_name` varchar(255) DEFAULT NULL,
  `disbursement_numbers` text DEFAULT NULL,
  
  -- حقول التبرعات المقيدة فقط
  `restriction_type` enum('family','project') DEFAULT NULL,
  `family_file_number` varchar(50) DEFAULT NULL,
  `project_type` varchar(100) DEFAULT NULL,
  
  -- حقول إضافية
  `notes` text DEFAULT NULL,
  `receipt_path` varchar(255) DEFAULT NULL,
  `qr_code` varchar(255) DEFAULT NULL,
  `serial_number` varchar(8) UNIQUE NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  FOREIGN KEY (`donor_id`) REFERENCES `donors`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`created_by`) REFERENCES `users`(`id`),
  INDEX `idx_serial_number` (`serial_number`),
  INDEX `idx_donation_type` (`donation_type`),
  INDEX `idx_date` (`date`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
